{
  "extends": "@tsconfig/recommended/tsconfig.json",
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "jsx": "react",
    "allowSyntheticDefaultImports": true,
    "moduleResolution": "node",
    "useDefineForClassFields": true,
    "lib": ["dom", "es5", "es6", "ES2022.Object", "ES2022.Array", "ES2022"],
    "baseUrl": "./",
    // "baseUrl": "./src",
    // "paths": {
    //   "@src/*": ["src/*"]
    // },
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "strict": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "strictNullChecks": true,
    "outDir": "./build"
  },
  "include": ["./src/**/*"]
}
