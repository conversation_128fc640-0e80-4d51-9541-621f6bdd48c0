import { Node } from "@xyflow/react";

export interface ColorTheme {
  primary: string;
  bg: string;
  bgHover: string;
  border: string;
}

export interface Employee {
  employee_code: string;
  display_name: string;
  display_pic: string;
  job_title: string;
  email?: string;
  gender?: string;
  department?: string;
  business_unit?: string;
  tenure?: string;
  number_of_reportees?: number;
}

export interface MappedResource {
  estimated_weightage: number;
  goal_objective_id: string;
  employee_code: string;
  employee: Employee;
  mapped_resources: MappedResource[];
}

export interface ObjectiveResourceMap {
  goal_objective_id: string;
  goal_objective_title: string;
  estimated_weightage: number;
  mapped_resources: MappedResource[];
}

export interface ResourceAllocationData {
  goal_id?: string;
  status?: string;
  performance_review_cycle?: {
    name: string;
    goal_setting_enabled: boolean;
    performance_review_enabled: boolean;
  };
  objective_resource_map: ObjectiveResourceMap[];
}

export interface ResourceAllocationProps {
  resourceData: ResourceAllocationData;
  onBack: () => void;
}

export interface CustomNodeRA extends Node {
  data: {
    goal_objective_title?: string;
    estimated_weightage: number;
    display_name?: string;
    job_title?: string;
    display_pic?: string | null;
    totalWeightage: number;
    objectives: Array<{
      goal_objective_title: string;
      estimated_weightage: number;
      employee: {
        display_pic: string | null;
        display_name: string;
        employee_code: string;
      };
    }>;
    hasChildren: boolean;
    colorTheme: ColorTheme;
  };
}

export interface ResourceAllocationNodeProps {
  id: string;
  data: {
    goal_objective_title?: string;
    estimated_weightage: number;
    display_name?: string;
    job_title?: string;
    display_pic?: string;
    totalWeightage: number;
    objectives: Array<{
      goal_objective_title: string;
      estimated_weightage: number;
      employee: {
        display_pic: string;
        display_name: string;
        employee_code: string;
      };
    }>;
    colorTheme: {
      primary: string;
      bg: string;
      bgHover: string;
      border: string;
    };
  };
}

// Color themes array
export const OBJECTIVE_COLORS: ColorTheme[] = [
  {
    primary: "#e11d48", // Deep Red
    bg: "#fef2f2",
    bgHover: "#fee2e2",
    border: "#fecaca",
  },
  {
    primary: "#2563eb", // Royal Blue
    bg: "#eff6ff",
    bgHover: "#dbeafe",
    border: "#bfdbfe",
  },
  {
    primary: "#9333ea", // Deep Purple
    bg: "#f3e8ff",
    bgHover: "#e9d5ff",
    border: "#d8b4fe",
  },
  {
    primary: "#facc15", // Gold Yellow
    bg: "#fefce8",
    bgHover: "#fef9c3",
    border: "#fde68a",
  },
  {
    primary: "#c026d3", // Vivid Magenta
    bg: "#fdf4ff",
    bgHover: "#fae8ff",
    border: "#f5d0fe",
  },
  {
    primary: "#d97706", // Bright Orange
    bg: "#fff7ed",
    bgHover: "#ffedd5",
    border: "#fed7aa",
  },
  {
    primary: "#f43f5e", // Flamingo Pink
    bg: "#fef2f2",
    bgHover: "#fee2e2",
    border: "#fecdd3",
  },
  {
    primary: "#fb923c", // Bright Amber
    bg: "#fff7ed",
    bgHover: "#ffedd5",
    border: "#fed7aa",
  },
];

export const defaultEdgeStyle = {
  strokeWidth: 2,
};
