import { Grid2 } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { Goal } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import GoalCard from "./GoalCards";
import { GoalSettingStates } from "./GoalSettings";

interface Props {
  setCurrentMode: (mode: GoalSettingStates) => void;
  setSelectedGoalDetails: (goal: Goal) => void;
}

const ViewGoalSettings: React.FC<Props> = ({ setCurrentMode, setSelectedGoalDetails }) => {
  const statusesToEnableUpdatationOrCreation = ["Not Started", "Draft", "Sent Back"];

  const { data: goals = [], isFetching } = useQuery(
    ["goals"],
    async () => {
      return performanceManagementService.getGoals();
    },
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );

  const onAddGoalClick = (goal: Goal) => {
    setSelectedGoalDetails(goal);
    setCurrentMode(GoalSettingStates.ADD_GOAL);
  };

  const onViewAddedGoalClick = (goal: Goal) => {
    setSelectedGoalDetails(goal);
    setCurrentMode(GoalSettingStates.VIEW_ADDED_GOALS);
  };

  const getGoalStates = (goal: Goal) => {
    if (goal.status === "Approved") {
      return {
        addGoalEnabled: false,
        disabled: false,
      };
    }

    if (!goal.enabled) {
      return {
        addGoalEnabled: false,
        disabled: true,
      };
    }

    if (goal.enabled && goal.status === "Submitted") {
      return {
        addGoalEnabled: false,
        disabled: false,
      };
    }
    return {
      addGoalEnabled:
        (goal?.performance_review_cycle?.goal_setting_enabled &&
          statusesToEnableUpdatationOrCreation.includes(goal.status)) ||
        false,
      disabled: false,
    };
  };

  return (
    <Grid2 container spacing={2}>
      {!isFetching &&
        goals.map((goal, index) => (
          <Grid2 size={{ xs: 12, sm: 6, md: 4 }} key={index}>
            <GoalCard
              quarter={goal?.performance_review_cycle?.name || ""}
              isAddGoal={getGoalStates(goal).addGoalEnabled}
              iconIndex={index % 4}
              onAddGoalClick={() => onAddGoalClick(goal)}
              onViewAddedGoalClick={() => onViewAddedGoalClick(goal)}
              status={goal.status}
              buttonTitle={goal.status === "Not Started" ? "Create Goal" : "Update Goal"}
              reason={goal?.comment || ""}
              isDisabled={getGoalStates(goal).disabled}
            />
          </Grid2>
        ))}
    </Grid2>
  );
};

export default ViewGoalSettings;
