import { ArrowRightAltSharp, InfoOutlined } from "@mui/icons-material";
import { Box, Button, Card, IconButton, Paper, Tooltip, Typography } from "@mui/material";
// GoalCard.jsx
import React from "react";
import {
  PerformanceCardIconV1,
  PerformanceCardIconV2,
  PerformanceCardIconV3,
  PerformanceCardIconV4,
} from "src/assets/icons.svg";
import { getStatusColors } from "src/utils/typographyUtils";

interface GoalCardProps {
  quarter: string;
  status: string;
  isAddGoal: boolean;
  iconIndex: number;
  onAddGoalClick?: () => void;
  onViewAddedGoalClick?: () => void;
  buttonTitle: string;
  disableIcon?: boolean;
  reason?: string;
  isDisabled?: boolean;
}

const CardIconConfig = [PerformanceCardIconV1, PerformanceCardIconV2, PerformanceCardIconV3, PerformanceCardIconV4];

const GoalCard: React.FC<GoalCardProps> = ({
  quarter,
  status,
  isAddGoal,
  iconIndex,
  onAddGoalClick,
  onViewAddedGoalClick,
  buttonTitle = "",
  disableIcon = false,
  reason,
  isDisabled = false,
}) => {
  console.log(quarter, status, isAddGoal, iconIndex, reason);
  const Icon = CardIconConfig[iconIndex];
  return (
    <Tooltip placement="top" title={isDisabled ? "Goal setting period is over for this performance cycle" : ""}>
      <Card
        component={Paper}
        elevation={3}
        sx={{
          width: "100%",
          minHeight: 300,
          borderRadius: "15px",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          padding: "16px",
          marginBottom: "20px",
          position: "relative",
          opacity: isDisabled ? 0.5 : 1,
        }}
      >
        <Box display="flex" flexDirection="column" gap={1}>
          <Typography variant="h6" component="div" gutterBottom>
            {quarter}
          </Typography>
          <Box display="flex" gap={1} alignItems="center">
            <Typography variant="body2" color="text.secondary">
              Status: <span style={{ color: getStatusColors(status) }}>{status}</span>
            </Typography>
            {status === "Sent Back" && (
              <Tooltip title={reason}>
                <IconButton>
                  <InfoOutlined color="error" fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>
        <Box display="flex" justifyContent="space-between" alignItems="flex-end" width="100%">
          {!isAddGoal ? (
            !disableIcon && (
              <IconButton disabled={isDisabled} onClick={onViewAddedGoalClick} sx={{ background: "#E6F2F1" }}>
                <ArrowRightAltSharp />
              </IconButton>
            )
          ) : (
            <Button disabled={isDisabled} variant="contained" onClick={onAddGoalClick}>
              {buttonTitle}
            </Button>
          )}
          <Box position="absolute" right={0} bottom={-6}>
            <Icon />
          </Box>
        </Box>
      </Card>
    </Tooltip>
  );
};

export default GoalCard;
