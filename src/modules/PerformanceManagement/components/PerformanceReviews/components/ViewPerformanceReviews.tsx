import { Grid } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { PerformanceReview } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import GoalCard from "../../GoalCards";
import { PerformanceReviewFormStates } from "./PerformanceReview";

interface ViewPerformanceReviewsProps {
  setSelectedPerformanceReview: (goal: PerformanceReview) => void;
  setCurrentMode: (mode: PerformanceReviewFormStates) => void;
}

const ViewPerformanceReviews: React.FC<ViewPerformanceReviewsProps> = ({
  setSelectedPerformanceReview,
  setCurrentMode,
}) => {
  const statusesToEnableUpdatationOrCreation = ["Not Started", "Draft", "Sent Back"];

  const { data: reviews, isFetching } = useQuery(
    ["performance-reviews"],
    async () => {
      return performanceManagementService.getPerformanceReviews();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    },
  );

  const onAddPerformanceReviewClick = (performanceReview: PerformanceReview) => {
    setSelectedPerformanceReview(performanceReview);
    setCurrentMode(PerformanceReviewFormStates.ADD_REVIEW);
  };

  const onViewAddedPerformanceReviewClick = (performanceReview: PerformanceReview) => {
    setSelectedPerformanceReview(performanceReview);
    setCurrentMode(PerformanceReviewFormStates.VIEW_ADDED_REVIEWS);
  };

  return (
    <Grid container spacing={2}>
      {!isFetching &&
        reviews?.map((review, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <GoalCard
              quarter={review?.performance_review_cycle?.name || ""}
              isAddGoal={
                (review?.performance_review_cycle?.performance_review_enabled &&
                  statusesToEnableUpdatationOrCreation.includes(review?.status || "")) ||
                false
              }
              reason={review?.comment || ""}
              iconIndex={index % 4}
              onAddGoalClick={() => onAddPerformanceReviewClick(review)}
              onViewAddedGoalClick={() => onViewAddedPerformanceReviewClick(review)}
              status={review?.status || ""}
              buttonTitle={review?.status === "Not Started" ? "Create Review" : "Update Review"}
            />
          </Grid>
        ))}
    </Grid>
  );
};

export default ViewPerformanceReviews;
