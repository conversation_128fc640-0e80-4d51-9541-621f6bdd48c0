import { ArrowBack } from "@mui/icons-material";
import { <PERSON>, Divider, Grid2, Icon<PERSON>utton, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import performanceManagementService from "src/services/performanceManagement.service";
import { ResourceAllocationData } from "../ResourceAllocationTypes";
import GoalCard from "./GoalCards";
import ResourceAllocation from "./ResourceAllocation";

export enum ResourceAllocationStates {
  VIEW_CARDS,
  VIEW_ALLOCATION,
}

const ResourceAllocationWrapper = () => {
  const [currentMode, setCurrentMode] = useState<ResourceAllocationStates>(ResourceAllocationStates.VIEW_CARDS);
  const [selectedGoal, setSelectedGoal] = useState<ResourceAllocationData | null>(null);

  const { data: resourceAllocation = [], isFetching: isResourceFetching } = useQuery<ResourceAllocationData[]>(
    ["resourceAllocation"],
    () => performanceManagementService.getResourceAllocation(),
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );

  const onViewResourceAllocation = (goal: ResourceAllocationData) => {
    setSelectedGoal(goal);
    setCurrentMode(ResourceAllocationStates.VIEW_ALLOCATION);
  };

  const handleBackToCards = () => {
    setCurrentMode(ResourceAllocationStates.VIEW_CARDS);
    setSelectedGoal(null);
  };

  if (currentMode === ResourceAllocationStates.VIEW_ALLOCATION && selectedGoal) {
    // return ;
    return (
      <Box>
        <Box display="flex" flexDirection="row" gap={1} sx={{ margin: ["0px 0px 12px 0px"] }}>
          <Box display="flex" gap={2} alignItems="center">
            <IconButton onClick={handleBackToCards}>
              <ArrowBack />
            </IconButton>
            <Typography>Resource Allocation</Typography>
          </Box>
        </Box>
        <Divider />
        <ResourceAllocation resourceData={selectedGoal as ResourceAllocationData} onBack={handleBackToCards} />
      </Box>
    );
  }

  return (
    <Box m={1}>
      <Grid2 container spacing={2}>
        {!isResourceFetching &&
          resourceAllocation.map((resource, index) => {
            return (
              <Grid2 size={{ xs: 12, sm: 6, md: 4 }} key={resource.goal_id}>
                <GoalCard
                  quarter={resource.performance_review_cycle?.name || ""}
                  isAddGoal={false}
                  iconIndex={index % 4}
                  onViewAddedGoalClick={() => onViewResourceAllocation(resource)}
                  status={resource.status || ""}
                  buttonTitle="View Resource Allocation"
                  isDisabled={resource.status !== "Approved"}
                />
              </Grid2>
            );
          })}
      </Grid2>
    </Box>
  );
};

export default ResourceAllocationWrapper;
