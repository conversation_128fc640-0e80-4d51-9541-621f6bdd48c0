import { Box, Divider, Paper, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ScrollableBox from "src/modules/Common/Container/ScrollableBox";
import leavesService from "src/services/leaves.service";
import NoData from "./components/NoDataScreens/NoData";
import { HeaderContainer } from "./style";

const LeaveReports = () => {
  const { userDetails } = useAppSelector((state) => state.userManagement);

  const { data } = useQuery({
    queryKey: ["get-leave-summary"],
    queryFn: async () => leavesService.getLeaveSummary(),
    retryOnMount: false,
    refetchOnWindowFocus: false,
    enabled: userDetails?.organisations?.length > 0,
  });

  const totalLeaves = useMemo(() => data?.reduce((a, b) => a + b.noOfLeaves, 0), [data]);

  if (data?.length === 0) {
    return <NoData title="No leaves assigned yet" />;
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <HeaderContainer>
        <Typography>Leave Reports</Typography>
        <Typography>{`Total ${totalLeaves}`}</Typography>
      </HeaderContainer>
      <Divider />
      <ScrollableBox maxHeight={250}>
        <Paper elevation={0}>
          {data?.map((leaveSummary) => (
            <Box
              key={leaveSummary.leaveType}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              padding="10px 0px"
            >
              <Typography fontSize={14}>{leaveSummary.leaveType}</Typography>
              <Typography fontSize={14} fontWeight={600}>
                {leaveSummary.noOfLeaves}
              </Typography>
            </Box>
          ))}
        </Paper>
      </ScrollableBox>
    </Box>
  );
};

export default LeaveReports;
