import { Box, Container, Divider, Link, Switch } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_ColumnDef, MRT_Row } from "material-react-table";
import React, { useCallback, useState } from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import TableActions from "src/modules/Common/Table/TableActions";
import DeleteConfirmationModal from "src/modules/Settings/components/Common/DeleteConfirmationModal";
import { PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { PayrollTemplateProps, PayrollViewModes } from "./Payroll";
import PreviewTemplate from "./PreviewTemplate";

const AllTemplates: React.FC<PayrollTemplateProps> = ({ setCurrentSelectedMode, setSelectedRow, selectedRow }) => {
  const [templateToDelete, setTemplateToDelete] = useState<string | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState<boolean>(false);
  const [jobTitlesToShow, setJobTitlesToShow] = useState<string[]>([]);
  const { data, isLoading, isFetching, refetch } = useQuery(
    ["payroll-templates"],
    async () => payrollService.getAllTemplatesV2(),
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    },
  );
  const deleteMutation = useMutation({
    mutationFn: async (payload: string) => payrollService.deleteTemplate(payload),
    onSuccess: async () => {
      setTemplateToDelete(null);
      await refetch();
    },
  });

  const softDeleteMutation = useMutation({
    mutationFn: async (payload: { mode: "activate" | "deactivate"; name: string }) =>
      payrollService.softDelete(payload.mode, payload.name),
    onSuccess: async () => {
      await refetch();
    },
  });

  const globalFilterFn = useCallback((row: MRT_Row<PayrollTemplateV2>, columnId: string, filterValue: string) => {
    const value = row.getValue(columnId);
    const doesContainKeyword = String(value).toLowerCase().includes(filterValue.toLowerCase());
    if (Array.isArray(row.original["job_titles"])) {
      const isMatch = row.original.job_titles.some((item) => item.toLowerCase().includes(filterValue.toLowerCase()));

      return isMatch || doesContainKeyword;
    }
    return doesContainKeyword;
  }, []);

  const onCheck = async (checked: boolean, name: string) => {
    softDeleteMutation.mutate({ mode: checked ? "activate" : "deactivate", name });
  };

  const columns = (): MRT_ColumnDef<PayrollTemplateV2, any>[] => {
    return [
      {
        accessorKey: "name",
        header: "Template Name",
        size: 200,
        Cell: ({ row }) => (
          <Link
            component="button"
            sx={{ cursor: "pointer" }}
            underline="hover"
            fontWeight="bold"
            onClick={() => onView(row.original)}
          >
            {row.original.name}
          </Link>
        ),
      },
      {
        accessorKey: "description",
        header: "Description",
        size: 350,
      },
      {
        accessorKey: "country",
        header: "Country",
        size: 100,
      },
      {
        accessorKey: "job_titles",
        header: "Applicable Job Roles",
        size: 200,
        accessorFn: (row) => {
          const jobTitles = row.job_titles as string[];
          return (
            <Link
              component="button"
              sx={{ cursor: "pointer" }}
              underline="hover"
              fontWeight="bold"
              onClick={() => setJobTitlesToShow(jobTitles)}
            >
              {jobTitles?.length} Job Titles
            </Link>
          );
        },
      },
    ];
  };

  const onEdit = (row: PayrollTemplateV2) => {
    if (setSelectedRow) {
      setSelectedRow(row);
    }
    setCurrentSelectedMode(PayrollViewModes.EDIT);
  };
  const onDelete = (row: PayrollTemplateV2) => {
    setTemplateToDelete(row.name);
  };

  const onDeleteConfirmation = () => {
    deleteMutation.mutate(templateToDelete || "");
  };
  const onView = (row: PayrollTemplateV2) => {
    if (setSelectedRow) {
      setSelectedRow(row);
    }
    setIsPreviewMode(true);
  };

  const onAddTemplateClick = () => {
    setCurrentSelectedMode(PayrollViewModes.ADD);
  };

  return (
    <Container maxWidth="xl" disableGutters>
      <ContentHeader
        title="Compensation"
        subtitle="Configure Compensation Templates"
        buttonTitle="Add Template"
        primaryAction={onAddTemplateClick}
      />
      <Divider sx={{ margin: "10px 0px" }} />
      <DataTable
        layoutMode="grid"
        data={data || []}
        initialState={{
          columnPinning: {
            right: ["mrt-row-actions"],
          },
        }}
        state={{
          showSkeletons: isLoading && isFetching,
        }}
        displayColumnDefOptions={{
          "mrt-row-actions": {
            header: "",
            size: 190,
            enablePinning: true,
          },
        }}
        columns={columns()}
        enableRowActions
        enableColumnFilters
        enableGlobalFilter
        enableTopToolbar
        positionActionsColumn="last"
        filterFns={{
          filterPayrollTemplates: (row, columnId, filterValue) =>
            globalFilterFn(row as MRT_Row<PayrollTemplateV2>, columnId, filterValue),
        }}
        globalFilterFn="filterPayrollTemplates"
        renderRowActions={({ row }) => (
          <Box display="flex" gap={1}>
            <Switch
              value={row.original.active}
              checked={row.original.active}
              onChange={(_ev, checked) => onCheck(checked, row?.original?.name)}
            />
            <TableActions
              edit={{ onClick: () => onEdit(row.original), color: "primary" }}
              remove={{ onClick: () => onDelete(row.original), color: "error" }}
              view={{ onClick: () => onView(row.original), color: "primary", hide: true }}
            />
          </Box>
        )}
      />
      <DeleteConfirmationModal
        isModalOpen={!!templateToDelete}
        title="Are you sure you want to delete this template?"
        selectedRole={templateToDelete || ""}
        onCancel={() => setTemplateToDelete(null)}
        onDelete={onDeleteConfirmation}
      />
      <Modal
        showBackButton
        title="Preview Template"
        isOpen={isPreviewMode && !!selectedRow}
        onClose={() => {
          setIsPreviewMode(false);
          if (setSelectedRow) {
            setSelectedRow(null);
          }
        }}
      >
        <PreviewTemplate
          componentsToPreview={
            selectedRow?.components?.map((eachComponent) => ({
              ...eachComponent?.compensation_component,
              amount: eachComponent?.amount,
            })) || []
          }
        />
      </Modal>
      <Modal
        showBackButton
        title="Job Titles"
        subtitle="Applicable Job Titles"
        isOpen={jobTitlesToShow.length > 0}
        onClose={() => {
          setJobTitlesToShow([]);
        }}
      >
        <DataTable
          data={jobTitlesToShow?.map((eachJobTitle) => ({ name: eachJobTitle })) || []}
          columns={[
            {
              accessorKey: "name",
              header: "Job Title",
            },
          ]}
        />
      </Modal>
    </Container>
  );
};

export default AllTemplates;
