import { <PERSON><PERSON>, Box, Button, Grid2, Paper, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo } from "react";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { Formula } from "src/modules/Employees/Compensation/Compensation";
import { CompensationAttributeKeys, CompensationComponent } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { z } from "zod";
import { CompensationComponentContext, getTaxabilityAlertTitles } from "../CompensationComponents";
import { InfoOutlined } from "@mui/icons-material";

const earningsSchema = z
  .object({
    name: z.string().min(1, {
      message: "Name is required",
    }),
    calculationType: z.enum(["Flat", "Percentage"]),
    amount: z.number().gt(0, {
      message: "",
    }),
    payType: z.enum(["Fixed", "Variable"]),
    percentageOf: z.string().optional().nullish(),
  })

type Props = {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  selectedRow: CompensationComponent | null;
  components: CompensationComponent[];
};

const earningConfigurations: {
  key: CompensationAttributeKeys;
  label: string;
  value: boolean | string | any;
  readonly?: boolean;
  options?: { label: string; value: string }[];
}[] = [
    {
      key: "consider_for_esi",
      label: "Consider for ESI Contribution",
      value: false,
      readonly: false,
    },
    {
      key: "include_in_fbp",
      label: "Include as a Flexible Benefit Plan component",
      value: false,
      readonly: false,
    },
    {
      key: "consider_for_epf",
      label: "Consider for EPF",
      readonly: false,
      value: {
        checked: false,
        radio_group_value: null,
      },
      options: [
        { label: "Always", value: "ALWAYS" },
        { label: "Only when PF wage is less than Rs 15,000", value: "RESTRICTED" },
      ],
    },
    {
      key: "ad_hoc",
      label: "This is an ad-hoc component",
      value: false,
      readonly: false,
    },
    {
      key: "pro_rated",
      label: "Pro-rated",
      value: false,
      readonly: false,
    },
  ];

const AddEditEarnings: React.FC<Props> = ({ isOpen, onClose, title, selectedRow, components }) => {
  const { refetch } = React.useContext(CompensationComponentContext);
  const isEditMode = useMemo(() => !!selectedRow, [selectedRow]);
  const componentNamesToDisplayAsOptionsOfPercentage = useMemo(() => {
    const defaultComponents = components?.map((eachComponent) => ({
      label: eachComponent.name,
      value: eachComponent.name,
    }));
    defaultComponents.push({ label: "CTC", value: "CTC" });
    defaultComponents.push({ label: "Gross", value: "Gross" });
    return defaultComponents;
  }, [components]);

  const createEarningMutation = useMutation({
    mutationKey: ["create-earning"],
    mutationFn: async (payload: Partial<CompensationComponent>) => payrollService.createCompensationComponent(payload),
    onSuccess: () => {
      refetch();
      onClose();
    },
  });

  const updateEarningMutation = useMutation({
    mutationKey: ["update-earning"],
    mutationFn: async (payload: Partial<CompensationComponent>) => payrollService.updateCompensationComponent(payload),
    onSuccess: () => {
      refetch();
      onClose();
    },
  });

  const getValue = (
    key: CompensationAttributeKeys,
    defaultValue: boolean | string,
    selectedRow: CompensationComponent | null,
  ) => {
    if (isEditMode) {
      const selectedRowValue = selectedRow?.attributes?.find((eachAttribute) => Reflect.has(eachAttribute, key))?.[key];
      if (key === "consider_for_epf") {
        return {
          checked: !!selectedRowValue,
          radio_group_value: selectedRowValue,
        };
      }
      return selectedRowValue || defaultValue;
    }
    return defaultValue;
  };

  const getComponentAttributes = (configurations: any) => {
    return configurations.reduce((acc, eachConfig) => {
      if (eachConfig.key === "consider_for_epf") {
        acc[eachConfig.key] = eachConfig.value.radio_group_value || null;
        return acc;
      }
      acc[eachConfig.key] = eachConfig.value;
      return acc;
    }, {});
  };

  const getCode = (name: string, formula: Formula) => {
    if (formula?.code) {
      return formula.code;
    }
    if (name === "CTC") {
      return "ctc";
    }
    if (name === "Gross") {
      return "gross";
    }
    return components?.find((eachComponent) => eachComponent.name === name)?.code;
  };

  const form = useAppForm({
    defaultValues: {
      ...selectedRow,
      id: selectedRow?.id || "",
      name: selectedRow?.name || "",
      calculationType: selectedRow?.formula?.calculation_type || "Flat",
      amount: selectedRow?.formula?.value || 0,
      payType: selectedRow?.pay_type || "Fixed",
      configurations: earningConfigurations.map((eachConfig) => ({
        ...eachConfig,
        value: getValue(eachConfig.key, eachConfig.value, selectedRow),
        readonly:
          selectedRow?.attributes?.find((eachAttribute) => Reflect.has(eachAttribute, eachConfig.key))?.read_only ||
          selectedRow?.system_defined,
      })),
    },
    validators: {
      onSubmit: ({ value }) => {
        if (calculationType === "Percentage" && !value?.formula?.code) {
          return {
            status: "error",
            errors: {
              percentageOf: "Percentage of is required",
            },
          };
        }
        return earningsSchema.safeParse(value);
      },
    },
    onSubmit: ({ value }) => {
      const request: Partial<CompensationComponent> = {
        id: value.id,
        name: value.name,
        component_type: "Earning",
        pay_type: value.payType,
        formula: {
          value: value.amount,
          code: value.calculationType === "Percentage" ? getCode(value.percentageOf, value.formula) : null,
          calculation_type: value.calculationType,
        },
        mandatory: true,
        pro_rated: false,
        taxable: false,
        ...getComponentAttributes(value.configurations),
      };

      try {
        if (isEditMode) {
          updateEarningMutation.mutate(request);
          return;
        }
        createEarningMutation.mutate(request);
        form.reset();
      } catch (error) {
        console.log(error);
      }
    },
  });

  const calculationType = useStore(form.store, (state) => state.values.calculationType);

  const inputProps = earningConfigurations.map((eachConfig, index) => {
    return {
      fieldProps: {
        name: `configurations.${index}.value`,
        mode: "array",
      },
      formProps: {
        type: eachConfig.key === "consider_for_epf" ? "radio-group-with-checkbox" : "checkbox",
        label: eachConfig.label,
        required: false,
        layout: eachConfig.key === "consider_for_epf" ? "vertical" : "horizontal",
        options: eachConfig.options || [],
        disabled: form.getFieldValue(`configurations.${index}.readonly`),
      },
      containerProps: {
        size: 6,
      },
    };
  });

  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {
        onClose();
        form.reset();
      }}
      showBackButton
      title={title}
      actions={
        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine, state.errorMap]}>
          {([canSubmit, isSubmitting, isPristine, errorMap]) => {
            console.log({ errorMap });
            
            return (
              <Box display="flex" p={2} gap={1} justifyContent="flex-end">
                <Button variant="outlined" onClick={() => onClose()}>
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  disabled={!canSubmit || isPristine || isSubmitting}
                  onClick={form.handleSubmit}
                >
                  Save
                </Button>
              </Box>
            );
          }}
        </form.Subscribe>
      }
    >
      <Grid2 container spacing={2}>
        <Grid2 size={12}>
          <form.AppField name="name">
            {(field: any) => <field.EffiTextField label="Earning Name" required />}
          </form.AppField>
        </Grid2>
        <Grid2 size={6} gap={2}>
          <Box
            display={selectedRow?.formula?.value === "System Defined" ? "none" : "block"}
            component={Paper}
            elevation={2}
            p={2}
            height={150}
            gap={6}
          >
            <form.AppField name="calculationType">
              {(field: any) => (
                <field.EffiRadioGroup
                  label="Calculation Type"
                  layout="horizontal"
                  required
                  options={[
                    { label: "Flat Amount", value: "Flat" },
                    { label: "Percentage", value: "Percentage" },
                  ]}
                />
              )}
            </form.AppField>
            <Box display="flex" gap={2}>
              <form.AppField name="amount">
                {(field: any) => {
                  if (calculationType === "Percentage") {
                    return (
          
                        <field.EffiPercentageField
                          label=""
                          required
                          endHelperText={
                            isEditMode && form.getFieldValue("formula.code")
                              ? `of ${selectedRow?.formula?.display_name}`
                              : ""
                          }
                          placeholder="Enter percentage"
                          sx={{ width: !isEditMode ? 100 : "100%" }}
                        />
                      </Box>
                    );
                  }
                  return <field.EffiCurrency label="" required currency="INR" placeholder="Enter amount" />;
                }}
              </form.AppField>
              {calculationType === "Percentage" && !form.getFieldValue("formula.code") && (
                <Typography alignSelf="center">of</Typography>
              )}
              {calculationType === "Percentage" && !form.getFieldValue("formula.code") && (
                <form.AppField name="percentageOf">
                  {(field: any) => (
                    <field.EffiSelect
                      label=""
                      required
                      options={componentNamesToDisplayAsOptionsOfPercentage}
                      placeholder="Select component"
                      sx={{ width: 200 }}
                    />
                  )}
                </form.AppField>
              )}
            </Box>
          </Box>
        </Grid2>
        <Grid2 display={selectedRow?.formula?.value === "System Defined" ? "none" : "block"} size={6} gap={1}>
          <Box component={Paper} elevation={2} p={2} height={150}>
            <form.AppField name="payType">
              {(field: any) => (
                <field.EffiRadioGroup
                  label="Pay Type"
                  layout="vertical"
                  required
                  options={[
                    { label: "Fixed Pay", value: "Fixed", subLabel: "Fixed amount paid at the end of every month" },
                    { label: "Variable Pay", value: "Variable", subLabel: "Variable amount paid any payroll" },
                  ]}
                />
              )}
            </form.AppField>
          </Box>
        </Grid2>
        <Grid2 size={12}>
          <Typography lineHeight={2} variant="h6" fontWeight={600} fontSize={18}>
            Configurations
          </Typography>
          <Box display="flex" flexDirection="column" component={Paper} elevation={2} p={2} overflow="auto" gap={2}>
            {form.getFieldValue("taxability") && <Alert icon={<InfoOutlined color="success" />} severity="success">{getTaxabilityAlertTitles(form.getFieldValue("taxability"))}</Alert>}
            <EffiDynamicForm form={form} inputFields={inputProps} />
          </Box>
        </Grid2>
      </Grid2>
    </Modal>
  );
};

export default AddEditEarnings;
