import { CancelRounded, CheckCircleRounded } from "@mui/icons-material";
import { Box, Link, Switch, Tooltip, Typography } from "@mui/material";
import React from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import TableActions from "src/modules/Common/Table/TableActions";
import { CompensationComponent } from "src/services/api_definitions/payroll.service";
import { CompensationComponentContext } from "../CompensationComponents";

const ViewDeductions: React.FC<{
  deductions: CompensationComponent[];
}> = ({ deductions }) => {
  const compensationComponentContext = React.useContext(CompensationComponentContext);

  const flattenAttributes = (attributes: Record<string, any>[]) => {
    const flat: Record<string, any> = {};
    attributes.forEach((attr) => {
      Object.entries(attr).forEach(([key, value]) => {
        flat[key] = value;
      });
    });
    return flat;
  };

  const transformedData = deductions.map((row) => ({
    ...row,
    ...flattenAttributes(row.attributes),
  }));

  const renderBooleanCells = (isChecked: boolean) => {
    return isChecked ? <CheckCircleRounded color="success" /> : <CancelRounded color="error" />;
  };

  const getFrequencyTitles = (frequency: string) => {
    if (frequency === "ONE_TIME") {
      return "One time";
    }
    if (frequency === "RECURRING") {
      return "Recurring";
    }
    return null;
  };

  return (
    <DataTable
      data={transformedData}
      state={{
        showSkeletons: compensationComponentContext.isLoading,
        columnPinning: {
          right: ["mrt-row-actions"],
          left: ["active"],
        },
      }}
      displayColumnDefOptions={{
        "mrt-row-actions": {
          size: 100,
          maxSize: 100,
          header: "",
          muiTableBodyCellProps: {
            align: "right",
          },
        },
      }}
      enableRowActions
      renderRowActions={({ row }) => (
        <Box display="flex" gap={1} alignItems="center" justifyContent="flex-end">
          <TableActions
            edit={{
              onClick: () => {
                compensationComponentContext.onEdit(row.original);
              },
              hide: true,
            }}
            remove={{
              onClick: () => {
                compensationComponentContext.deleteComponent(row.original.id);
              },
              hide: row.original.system_defined,
            }}
            view={{
              onClick: () => {
                compensationComponentContext.onView(row.original);
              },
              hide: true,
            }}
          />
          <Tooltip title={row.original.active ? "Deactivate" : "Activate"}>
            <Switch
              onChange={(_ev, checked) => compensationComponentContext.softDeleteComponent(row.original.id, checked)}
              checked={row.original.active}
              disabled={row.original.mandatory}
            />
          </Tooltip>
        </Box>
      )}
      columns={[
        {
          accessorKey: "name",
          header: "Component",
          size: 150,
          Cell: ({ cell, row }) => (
            <Link
              fontWeight="bold"
              underline="hover"
              sx={{ cursor: "pointer" }}
              onClick={() => compensationComponentContext.onEdit(row?.original)}
            >
              {cell.getValue()}
            </Link>
          ),
        },
        {
          accessorKey: "applied_frequency",
          header: "Applied Frequency",
          Cell: ({ cell }) => <Typography>{getFrequencyTitles(cell.getValue<string>())}</Typography>,
        },
        {
          accessorKey: "ad_hoc",
          header: "Ad-hoc",
          Cell: ({ cell }) => renderBooleanCells(cell.getValue<boolean>()),
          size: 50,
          muiTableBodyCellProps: {
            align: "center",
          },
          muiTableHeadCellProps: {
            align: "center",
          },
        },
      ]}
    />
  );
};

export default ViewDeductions;
