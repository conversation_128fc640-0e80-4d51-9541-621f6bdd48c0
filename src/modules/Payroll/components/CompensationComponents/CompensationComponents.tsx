import { Box, Button } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import TabsView from "src/modules/Common/CustomTabs/CustomTabs";
import { CompensationComponent } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import AddEditBenefits from "./Benefits/AddEditBenefits";
import ViewBenefits from "./Benefits/ViewBenefits";
import AddEditDeductions from "./Deductions/AddEditDeductions";
import ViewDeductions from "./Deductions/ViewDeductions";
import AddEditEarnings from "./Earnings/AddEditEarnings";
import ViewEarnings from "./Earnings/ViewEarnings";
import { useMasterData } from "src/customHooks/useMasterData";
import { Option } from "src/app/global";

export const getTaxabilityAlertTitles = (taxability?: string | null) => {
    if (taxability === "FULLY_TAXABLE") {
      return "This component is fully taxable";
    }
    if (taxability === "PARTIALLY_EXEMPT") {
      return "This component is partially exempt from tax";
    }
    if (taxability === "FULLY_EXEMPT") {
      return "This component is fully exempt from tax";
    }
    return null;
  };

export const CompensationComponentContext = React.createContext({
  refetch: () => {},
  isLoading: false,
  onEdit: (row: CompensationComponent) => {},
  onDelete: (row: CompensationComponent) => {},
  onView: (row: CompensationComponent) => {},
  setSelectedRow: (row: CompensationComponent | null) => {},
  selectedRow: null as CompensationComponent | null,
  softDeleteComponent: (id: string, active: boolean) => {},
  deleteComponent: (id: string) => {},
  appliedFrequencies: [] as Option<string, string>[],
});

const compensationMaps = [
  {
    title: {
      create: "Add Earnings",
      edit: "Edit Earnings",
    },
    key: "Earning",
    Component: AddEditEarnings,
  },
  {
    title: {
      create: "Add Deductions",
      edit: "Edit Deductions",
    },
    key: "Deduction",
    Component: AddEditDeductions,
  },
  {
    title: {
      create: "Add Benefits",
      edit: "Edit Benefits",
    },
    key: "Benefit",
    Component: AddEditBenefits,
  },
];

const categoriseCompensationComponents = (compensationComponent: CompensationComponent[]) => {
  return compensationComponent.reduce(
    (acc, item) => {
      if (!acc[item.component_type]) {
        acc[item.component_type] = [];
      }
      acc[item.component_type].push(item);
      return acc;
    },
    {} as Record<string, CompensationComponent[]>,
  );
};

const CompensationComponents = () => {
  const [activeTab, setActiveTab] = React.useState(0);
  const selectedTab = useMemo(() => compensationMaps[activeTab], [activeTab]);
  const [isAddModalOpen, setIsAddModalOpen] = React.useState(false);
  const [selectedRow, setSelectedRow] = React.useState<CompensationComponent | null>(null);
  const {
    data: compensationComponents,
    refetch,
    isLoading,
  } = useQuery(["get-compensation-components"], async () => {
    const compensationComponents = await payrollService.getAllCompensationComponents("India", false);
    return categoriseCompensationComponents(compensationComponents as CompensationComponent[]) as Record<
      string,
      CompensationComponent[]
    >;
  }, {
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
  });
  const { data: appliedFrequencies } = useMasterData("AppliedFrequency");

  const handleChange = (value: number) => {
    setActiveTab(value);
  };

  const handleAddCompensationTemplate = (_activeTab: number) => {
    setIsAddModalOpen(true);
  };

  const onEdit = (row: CompensationComponent) => {
    setIsAddModalOpen(true);
    setSelectedRow(row);
  };

  const onDelete = (row: CompensationComponent) => {
    setIsAddModalOpen(true);
  };

  const onView = (row: CompensationComponent) => {
    setIsAddModalOpen(true);
  };

  const softDeleteComponent = async (id: string, active: boolean) => {
    try {
      await payrollService.changeCompensationComponentStatus(id, active);
    } catch (err) {
      console.log({ err });
    }
    refetch();
  };

  const deleteComponent = async (id: string) => {
    try {
      await payrollService.deleteCompensationComponent(id);
    } catch (err) {
      console.log({ err });
    }
    refetch();
  };

  const getLabelForAppliedFrequency = (value: string) => {
    if (value === "ONE_TIME") {
      return "One time benefit";
    }
    if (value === "RECURRING") {
      return "Recurring benefit for subsequent Payrolls";
    }
    return value;
  };

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader
        title="Compensation Components"
        actions={
          <Button sx={{ width: 200 }} variant="contained" onClick={() => handleAddCompensationTemplate(activeTab)}>
            {compensationMaps[activeTab]?.title?.create}
          </Button>
        }
      />
      <CompensationComponentContext.Provider
        value={{
          refetch,
          isLoading,
          onEdit,
          onDelete,
          onView,
          setSelectedRow,
          selectedRow,
          softDeleteComponent,
          deleteComponent,
          appliedFrequencies: appliedFrequencies?.map((each) => ({
            label: getLabelForAppliedFrequency(each),
            value: each,
          })) || [] as any,
        }}
      >
        <TabsView
          handleTabChange={handleChange}
          tabs={[
            {
              id: 0,
              label: "Earnings",
              component: <ViewEarnings earnings={compensationComponents?.Earning || []} />,
            },
            {
              id: 1,
              label: "Deductions",
              component: <ViewDeductions deductions={compensationComponents?.Deduction || []} />,
            },
            {
              id: 1,
              label: "Benefits",
              component: <ViewBenefits benefits={compensationComponents?.Benefit || []} />,
            },
          ]}
        />
        <selectedTab.Component
          isOpen={isAddModalOpen}
          key={selectedRow?.id || ""}
          onClose={() => {
            setIsAddModalOpen(false);
            setSelectedRow(null);
          }}
          title={selectedRow ? selectedTab.title.edit : selectedTab.title.create}
          selectedRow={selectedRow}
          components={compensationComponents?.[selectedTab.key] || []}
        />
      </CompensationComponentContext.Provider>
    </Box>
  );
};

export default CompensationComponents;
