import { AddBoxOutlined, DeleteForever } from "@mui/icons-material";
import { Box, Button, DialogActions } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useParams } from "react-router-dom";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import Modal from "src/modules/Common/Modal/Modal";
import { CostCenterDetail } from "src/services/api_definitions/tenants";
import tenantsService from "src/services/tenants.service";
import validators from "src/utils/validators";

interface CostCenterModalProps {
  readonly onClose: () => void;
  readonly isModalOpen: boolean;
  readonly refetch: () => void;
}

type CostCenterFormState = {
  code: string;
};

const defaultFormState: CostCenterFormState[] = [
  {
    code: "",
  },
];

const AddCostCenterModal: React.FC<CostCenterModalProps> = ({ isModalOpen, onClose, refetch }) => {
  const { tenantId = "" } = useParams();
  const { formDetails, formErrors, handleChange, addNewFormDetailRow, deleteFormDetails, areFormDetailsValid } =
    useForm({
      initialState: defaultFormState,
      isBulk: true,
      validations: {
        code: [validators.validateInput],
      },
    });

  const createCostCenterMutation = useMutation({
    mutationKey: ["create-cost-centers"],
    mutationFn: async () => tenantsService.saveCostCenters(tenantId, formDetails as CostCenterDetail[]),
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const areFormDetailsFilled = useMemo(() => {
    return (formDetails as CostCenterDetail[]).every((formDetail) =>
      Object.values(formDetail).every((detail) => !!detail),
    );
  }, [formDetails]);

  const onAddMoreClick = () => {
    addNewFormDetailRow();
  };

  const onDeleteClick = (index: number) => {
    deleteFormDetails(index);
  };

  const onSaveClick = () => {
    createCostCenterMutation.mutate();
  };

  return (
    <Modal
      title={languageConfig.tenants.costCenter.modalTitle}
      subtitle={languageConfig.tenants.costCenter.modalSubtitle}
      showBackButton
      isOpen={isModalOpen}
      onClose={onClose}
      fullWidth
      actions={
        <DialogActions sx={{ margin: 2 }}>
          <Button
            disabled={!areFormDetailsValid || !areFormDetailsFilled}
            size="large"
            variant="contained"
            onClick={onSaveClick}
          >
            {languageConfig.tenants.button.save}
          </Button>
        </DialogActions>
      }
    >
      {(formDetails as CostCenterDetail[]).map((formDetail, index) => (
        <Box sx={{ width: "100%", margin: "30px 0px" }} key={`${index + 1}`}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <CustomTextField
              size="small"
              sx={{ flex: 6 }}
              placeholder="Cost center code"
              fullWidth
              id="code"
              value={formDetail.code}
              onChange={(ev) => handleChange(ev, index)}
              error={!!formErrors[index].code}
              helperText={!!formErrors[index].code && formErrors[index].code}
            />
            {(formDetails as CostCenterDetail[])?.length !== 1 && (
              <DeleteForever
                onClick={() => onDeleteClick(index)}
                sx={{ flex: 1, fill: "#667085", cursor: "pointer" }}
              />
            )}
          </Box>
          {index === (formDetails as CostCenterDetail[])?.length - 1 && (
            <Button
              startIcon={<AddBoxOutlined />}
              variant="text"
              onClick={onAddMoreClick}
              sx={{ borderRadius: 2, margin: "16px 0px" }}
            >
              Add More
            </Button>
          )}
        </Box>
      ))}
    </Modal>
  );
};

export default AddCostCenterModal;
