import { ReduceCapacityRounded } from "@mui/icons-material";
import { Box, Card, CardContent, IconButton, Tooltip, Typography } from "@mui/material";
import React from "react";
import Span from "src/modules/Common/Span/Span";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { HeaderProps } from "..";
import { ProfileDivider } from "../styles";
import DisplayTaglines from "./DisplayTagline";
import ProfilePhoto from "./components/ProfilePhoto";

export interface ProfileMetaData {
  name: string;
  jobTitle: string;
  department: string;
  companyName: string;
  email: string;
  dateOfJoining: string;
  officeLocation: string;
  displayPic: string;
  tenure: string;
}

interface ProfileCardProps extends HeaderProps<ProfileMetaData> {
  // onEditClick: () => void;
  goToOrgCharts: () => void;
  isEmployeeSearch?: boolean;
}

const ProfileCard: React.FC<ProfileCardProps> = ({ informationMetaData, goToOrgCharts, isEmployeeSearch = false }) => {
  return (
    <Card sx={{ flex: 1, boxShadow: "none", backgroundColor: "transparent", height: "100%" }}>
      <CardContent sx={{ height: "100%", display: "flex", alignItems: "center", padding: "20px" }}>
        <Box display="flex" alignItems="center" gap={4}>
          <ProfilePhoto employeeDisplayPicURL={informationMetaData?.displayPic} isEmployeeSearch={isEmployeeSearch} />
          <Box flexGrow={1}>
            <Box display="flex" justifyContent="space-between" alignItems="center" gap={2}>
              <Typography variant="h6" component="div" fontWeight={600}>
                {informationMetaData.name}
              </Typography>
              <Tooltip title="View Organisation Chart">
                <IconButton onClick={() => goToOrgCharts()}>
                  <ReduceCapacityRounded color="primary" fontSize="large" />
                </IconButton>
              </Tooltip>
            </Box>
            <Box display="flex" flexDirection="column" gap={1}>
              <DisplayTaglines
                variant="body1"
                title1={<Span color="primary.main">{informationMetaData.jobTitle}</Span>}
                title2={informationMetaData.department}
              />
              <DisplayTaglines
                variant="body2"
                title1={informationMetaData.companyName}
                title2={informationMetaData.email}
                isBold={true}
                showTooltip
              />
              <Box display="flex" alignItems="center" gap={1}>
                <DisplayTaglines
                  variant="caption"
                  title1="DOJ"
                  title2={formatDateToDayMonthYear(informationMetaData.dateOfJoining)}
                  isBold={true}
                  semiColon
                  seprator={false}
                />
                <ProfileDivider sx={{ margin: "4px 0px" }} />
                <DisplayTaglines
                  variant="caption"
                  title1="Tenure"
                  title2={informationMetaData.tenure}
                  isBold={true}
                  semiColon
                  seprator={false}
                />
              </Box>
              <Tooltip title="Office Address" placement="left">
                <Typography variant="caption" fontWeight={500}>
                  {informationMetaData.officeLocation}
                </Typography>
              </Tooltip>
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ProfileCard;
