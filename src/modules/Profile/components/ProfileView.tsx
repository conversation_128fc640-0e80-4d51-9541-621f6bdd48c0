import { Box, Grid, IconButton } from "@mui/material";
import React, { useMemo } from "react";
import EducationInformation from "./components/EducationInformation";
import PersonalInformation from "./components/PersonalInformation";

import { Edit } from "@mui/icons-material";
import Accordion from "src/modules/Common/Accordion/Accordion";
import LoadingScreen from "src/modules/Common/LoadingScreen/LoadingScreen";
import { stepsKey } from "src/modules/Employees/EmployeeStepper";
import { EmployeeDetails } from "src/services/api_definitions/employees";
import profileTransform from "src/services/data_transformers/profile.transform";
import { AccordionPropsTypes, ViewProps } from "..";
import { ProfileAccordionStyles } from "../styles";
import ProfileEditorForm from "./ProfileEditorForm";
import BankDetails from "./components/BankDetails";
import EmergencyContacts from "./components/EmergencyContacts";
import EmploymentHistory from "./components/EmploymentHistory";
import FamilyDetails from "./components/FamilyDetails";

const accordionsView = [
  {
    id: stepsKey.personalInformation,
    title: "Personal Information",
    Component: PersonalInformation,
    transformer: profileTransform.personalInformationAbstractor,
  },
  {
    id: stepsKey.familyDetails,
    title: "Family Details",
    Component: FamilyDetails,
    transformer: profileTransform.familyDetailsAbstractor,
  },
  {
    id: stepsKey.emergencyDetails,
    title: "Emergency Details",
    Component: EmergencyContacts,
    transformer: profileTransform.emergencyContactsAbstractor,
  },

  {
    id: stepsKey.educationDetails,
    title: "Educational Information",
    Component: EducationInformation,
    transformer: profileTransform.educationDetailsAbstractor,
  },
  {
    id: stepsKey.employmentHistory,
    title: "Employment History",
    Component: EmploymentHistory,
    transformer: profileTransform.workExperienceAbstractor,
  },
  {
    id: stepsKey.bankDetails,
    title: "Bank Information",
    Component: BankDetails,
    transformer: profileTransform.bankDetailAbstractor,
  },
];

interface ProfileViewProps extends ViewProps<EmployeeDetails | null | undefined> {
  isFetched: boolean;
  refetch: () => void;
}

interface ProfileComponentWrapperProps {
  Component: React.FC<any>;
  informationMetaData: EmployeeDetails | null | undefined;
  isViewMode: boolean;
  isOpen: boolean;
  onEditClick: (ev: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: string, title: string) => void;
  title: string;
  id: string;
  handleChange: (accordionId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => void;
  transformer: (data: EmployeeDetails | null | undefined) => any;
}

const ProfileComponentWrapper = ({
  Component,
  informationMetaData,
  isViewMode,
  isOpen,
  onEditClick,
  title,
  id,
  handleChange,
  transformer,
}: ProfileComponentWrapperProps) => {
  const data = transformer(informationMetaData);
  const hideEdit = id === stepsKey.bankDetails && Object.keys(data).length !== 0;
  return (
    <Accordion
      heading={
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, color: "#667085" }}>
          {title}
          {!isViewMode && !hideEdit && (
            <IconButton onClick={(ev) => onEditClick(ev, id, title)}>
              <Edit fontSize="medium" />
            </IconButton>
          )}
        </Box>
      }
      sx={ProfileAccordionStyles}
      isOpen={isOpen}
      handleChange={handleChange(title)}
      square
      disableGutters
      defaultExpanded
    >
      <Component isViewMode={isViewMode} informationMetaData={data as AccordionPropsTypes} />
    </Accordion>
  );
};

const ProfileView: React.FC<ProfileViewProps> = ({ informationMetaData, isFetched, refetch, isViewMode = false }) => {
  const [accordionController, setaccordionController] = React.useState(
    Object.fromEntries(accordionsView.map((item) => [item.title, true])),
  );
  const [activeEditForm, setActiveEditForm] = React.useState<{ id: string; title: string } | null>(null);

  const handleChange = (accordionId: string) => (__event: React.SyntheticEvent, isExpanded: boolean) => {
    setaccordionController((prevState) => ({
      ...prevState,
      [accordionId]: isExpanded,
    }));
  };

  const onEditClick = (ev: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: string, title: string) => {
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    (ev as any)?.stopPropagation();
    setActiveEditForm({ id, title });
  };

  const [lhsSection, rhsSection] = useMemo(() => {
    if (!isFetched) return [[], []];
    const filteredAccordions = accordionsView.filter((item) => {
      if (isViewMode) {
        const data = item.transformer(informationMetaData);
        const isEmpty = Array.isArray(data) ? data.length === 0 : Object.values(data).filter((v) => v).length === 0;
        return !isEmpty;
      }
      return true;
    });
    const middleIndex = Math.ceil(filteredAccordions.length / 2);
    return [filteredAccordions.slice(0, middleIndex), filteredAccordions.slice(middleIndex)];
  }, [isFetched, isViewMode, informationMetaData]);

  if (!isFetched) return <LoadingScreen />;

  return (
    <>
      <Grid container spacing={3} sx={{ mt: 0 }}>
        <Grid item xs={12} sm={6} md={6}>
          <Grid container spacing={3}>
            {lhsSection.map((compProps, index) => (
              <Grid item xs={12} sm={12} md={12} key={index}>
                <ProfileComponentWrapper
                  isViewMode={isViewMode}
                  onEditClick={onEditClick}
                  handleChange={handleChange}
                  isOpen={accordionController[compProps.title]}
                  informationMetaData={informationMetaData}
                  {...compProps}
                />
              </Grid>
            ))}
          </Grid>
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <Grid container spacing={3}>
            {rhsSection.map((compProps, index) => (
              <Grid item xs={12} sm={12} md={12} key={index}>
                <ProfileComponentWrapper
                  isViewMode={isViewMode}
                  onEditClick={onEditClick}
                  handleChange={handleChange}
                  isOpen={accordionController[compProps.title]}
                  informationMetaData={informationMetaData}
                  {...compProps}
                />
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Grid>
      {activeEditForm && (
        <ProfileEditorForm
          title={activeEditForm.title}
          currentForm={activeEditForm.id}
          onClose={() => setActiveEditForm(null)}
          formData={informationMetaData}
          refetcProfileDetails={refetch}
        />
      )}
    </>
  );
};

export default ProfileView;
