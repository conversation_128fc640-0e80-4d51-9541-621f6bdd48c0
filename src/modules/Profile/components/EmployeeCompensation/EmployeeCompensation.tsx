import { Visibility, VisibilityOff } from "@mui/icons-material";
import { <PERSON>, But<PERSON>, Container, Typography } from "@mui/material";
import { MRT_ColumnDef } from "material-react-table";
import React, { useMemo, useState } from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import { PayrollComponentV2, PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";

type Props = {
  compensation: PayrollComponentV2[];
  currency: string;
  locale?: string;
  ctc?: number;
  effectiveDate?: string;
  effectiveTo?: string;
  defaultAmountHidden?: boolean;
};

type ProcessedCompensationItem = PayrollComponentV2 & {
  isSection?: boolean;
  name?: string;
};

//Todo: Remove this any type and add proper types
export const formatCurrency = (amount: number, currency: string, locale: string = "IN") => {
  // this null undefined check is to show 0's if present in comp
  if (amount === null || amount === undefined || !locale) {
    return "";
  }
  return new Intl.NumberFormat(`en-${locale}`, {
    style: "currency",
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export const calculateTotalCTC = (data: PayrollComponentV2[] = []): number =>
  data.reduce((total, item) => total + (item?.amount || 0), 0);

const EmployeeCompensation: React.FC<Props> = ({
  compensation,
  locale = "IN",
  currency,
  ctc,
  effectiveDate,
  defaultAmountHidden = true,
  effectiveTo,
}) => {
  const compensationCurrency = currency || "INR";
  const [isAmountHidden, setIsAmountHidden] = useState(defaultAmountHidden);

  const getDisplayDates = (isCurrentCompensation: boolean, state: PayrollTemplateV2) => {
    if (isCurrentCompensation) {
      return `Effective from ${formatDateToDayMonthYear(state?.effective_from)} ${`${state?.effective_to ? `to ${formatDateToDayMonthYear(state?.effective_to)}` : ""}`}`;
    }
    return state?.effective_from ? `Effective from ${formatDateToDayMonthYear(state?.effective_from)}` : null;
  };

  const processedData = useMemo<ProcessedCompensationItem[]>(() => {
    // Group data by component_type
    const groupedData = compensation.reduce(
      (acc, item) => {
        const componentType = item.compensation_component.component_type;
        if (!acc[componentType]) {
          acc[componentType] = [];
        }
        acc[componentType].push(item);
        return acc;
      },
      {} as Record<string, PayrollComponentV2[]>,
    );

    const result: ProcessedCompensationItem[] = [];

    // Process each component type and add section headers
    Object.entries(groupedData).forEach(([componentType, components]) => {
      // Add section header
      result.push({
        compensation_component: {
          id: `section-${componentType}`,
          name: `${componentType}s`,
          code: "",
          currency: "",
          formula: { value: "", code: null, calculation_type: "", display_name: "" },
          mandatory: false,
          taxable: false,
          system_defined: false,
          pro_rated: false,
          pay_type: "",
          calculation_type: "",
          component_type: componentType,
          sort_order: null,
        },
        amount: 0,
        isSection: true,
        name: `${componentType}s`,
      });

      // Add components under this section
      components.forEach((component) => {
        result.push({
          ...component,
        });
      });
    });

    return result;
  }, [compensation]);

  const columns = useMemo<MRT_ColumnDef<ProcessedCompensationItem>[]>(
    () => [
      {
        accessorKey: "compensation_component.name",
        header: "Component",
        size: 300,
        grow: 1,
        Cell: ({ row }) => {
          if (row?.original?.isSection) {
            return (
              <Typography fontWeight={600} fontSize={16}>
                {row.original.name}
              </Typography>
            );
          }
          return row.original.compensation_component.name;
        },
      },
      {
        header: "Amount Monthly",
        size: 150,
        Cell: ({ row }) => {
          if (row?.original?.isSection) {
            return null;
          }
          return (
            <span
              style={{
                filter: isAmountHidden ? "blur(4px)" : "none",
                opacity: isAmountHidden ? 0.5 : 1,
                transition: "filter 0.3s ease, opacity 0.3s ease",
              }}
            >
              {formatCurrency(row.original.amount / 12, currency, locale)}
            </span>
          );
        },
        muiTableBodyCellProps: {
          align: "center",
        },
        muiTableHeadCellProps: {
          align: "center",
        },
      },
      {
        accessorKey: "amount",
        header: "Amount Annually",
        size: 150,
        Cell: ({ row }) => {
          if (row?.original?.isSection) {
            return null;
          }
          return (
            <span
              style={{
                filter: isAmountHidden ? "blur(4px)" : "none",
                opacity: isAmountHidden ? 0.5 : 1,
                transition: "filter 0.3s ease, opacity 0.3s ease",
              }}
            >
              {formatCurrency(row.original.amount, currency, locale)}
            </span>
          );
        },
        muiTableBodyCellProps: {
          align: "center",
        },
        muiTableHeadCellProps: {
          align: "center",
        },
      },
    ],
    [currency, locale, isAmountHidden],
  );

  return (
    <Container maxWidth="lg">
      <Box display="flex" alignItems="flex-end" justifyContent="space-between" p={1}>
        <Typography variant="body1" fontWeight={600}>
          Total CTC:{" "}
          <span
            style={{
              filter: isAmountHidden ? "blur(4px)" : "none",
              opacity: isAmountHidden ? 0.5 : 1,
              transition: "filter 0.3s ease, opacity 0.3s ease",
            }}
          >
            {formatCurrency(ctc as number, compensationCurrency, locale)}
          </span>
        </Typography>
        <Box display="flex" flexDirection="column" alignItems="flex-end" gap={2}>
          <Box>
            <Button
              role="textbox"
              variant="text"
              sx={{
                padding: 0,
                minWidth: 0,
                "&:hover": {
                  backgroundColor: "transparent",
                },
              }}
              onClick={() => setIsAmountHidden(!isAmountHidden)}
              startIcon={isAmountHidden ? <VisibilityOff /> : <Visibility />}
            >
              {isAmountHidden ? "Unmask" : "Mask"}
            </Button>
          </Box>
          <Typography variant="subtitle1">
            {getDisplayDates(!!effectiveTo, {
              effective_from: effectiveDate as string,
              effective_to: effectiveTo as string,
              name: "",
              country: "",
              job_titles: [],
              effective_date: "",
              template_name: "",
              description: null,
              active: false,
              employee_types: "",
              ctc: 0,
              components: [],
            })}
          </Typography>
        </Box>
      </Box>
      <DataTable
        layoutMode="grid"
        columns={columns}
        data={processedData || []}
        state={{
          expanded: true,
        }}
        enableStickyHeader
        muiTableContainerProps={{
          sx: {
            maxHeight: "100%",
            height: "100%",
          },
        }}
        muiTableBodyRowProps={({ row }) => ({
          sx: {
            backgroundColor: row.original.isSection ? "#F8FFFE" : "inherit",
          },
        })}
      />
    </Container>
  );
};

export default EmployeeCompensation;
