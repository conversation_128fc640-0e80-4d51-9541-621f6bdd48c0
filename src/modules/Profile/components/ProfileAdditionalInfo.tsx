import { Box, Grid, Typography } from "@mui/material";
import React from "react";
import { subtitle } from "src/modules/Common/ContentHeader/styles";

export interface InfoItemType {
  title: string;
  value: string;
}

export interface InfoList {
  jobTitle: InfoItemType[];
  jobStatus: InfoItemType[];
}

const InfoItem: React.FC<InfoItemType & { fontSize?: number }> = ({ title, value, fontSize = 16 }) => (
  <Box>
    {title && (
      <Typography color="text.secondary" display="flex" mr={1} fontSize={fontSize}>
        {title}
      </Typography>
    )}
    {subtitle && (
      <Typography display="flex" fontSize={fontSize} fontWeight={600}>
        {value}
      </Typography>
    )}
  </Box>
);

const ProfileAdditionalInfo = ({ infoList }: { infoList: InfoItemType[] }) => {
  return (
    <Grid container sx={{ flex: 1, alignContent: "center" }} gap={{ md: 2 }}>
      <Grid container xs={12} spacing={2}>
        {infoList.map((listItem, idx) => (
          <Grid item key={`${listItem}${idx}`} xs={12} md={4}>
            <InfoItem title={listItem.title} value={listItem.value} fontSize={14} />
          </Grid>
        ))}
      </Grid>
    </Grid>
  );
};

export default ProfileAdditionalInfo;
