import { Box, Container, Grid } from "@mui/material";
import React from "react";
import LoadingScreen from "src/modules/Common/LoadingScreen/LoadingScreen";
import { ProfileViewModes } from "src/pages/Profile";
import { EmployeeDetails } from "src/services/api_definitions/employees";
import profileTransform from "src/services/data_transformers/profile.transform";
import { HeaderProps } from "..";
import { ProfileDivider } from "../styles";
import ProfileAdditionalInfo from "./ProfileAdditionalInfo";
import ProfileCard from "./ProfileCard";

interface ProfileHeaderProps extends HeaderProps<EmployeeDetails | null | undefined> {
  isFetched: boolean;
  setCurrentViewMode?: (viewMode: ProfileViewModes) => void;
  isEmployeeSearch?: boolean;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  informationMetaData,
  isFetched,
  setCurrentViewMode,
  isEmployeeSearch = false,
}) => {
  if (!isFetched) return <LoadingScreen />;

  return (
    <Container
      // maxWidth="lg"
      maxWidth={false}
      disableGutters
      sx={{
        background: "white",
        // padding: "16px",
        borderRadius: 5,
      }}
    >
      <Grid container alignItems="stretch" sx={{ background: "#F8F8F8", borderRadius: 4, height: "100%" }}>
        <Grid item xs={12} sm={12} md={5.75} sx={{ padding: "0" }}>
          <ProfileCard
            isEmployeeSearch={isEmployeeSearch}
            goToOrgCharts={() => setCurrentViewMode?.(ProfileViewModes.ORG_CHART)}
            informationMetaData={profileTransform.profileCardDataAbstractor(informationMetaData)}
          />
        </Grid>
        <Grid
          item
          xs={12}
          sm={12}
          md={0.5}
          sx={{
            display: "flex",
            justifyContent: { md: "center" },
            flexDirection: { xs: "column", md: "row" },
            padding: { md: "20px 0" },
          }}
        >
          <ProfileDivider />
        </Grid>
        <Grid item xs={12} sm={12} md={5.75}>
          <Box sx={{ padding: { xs: 3, sm: 3, md: "20px 16px" } }}>
            <ProfileAdditionalInfo infoList={profileTransform.profileAdditionalInfo(informationMetaData)} />
          </Box>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ProfileHeader;
