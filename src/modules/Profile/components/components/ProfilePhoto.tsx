import EditIcon from "@mui/icons-material/Edit";
import { Avatar, Box, IconButton, Tooltip } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";

import { uploadProfilePic } from "src/modules/Employees/utils/utils";

import { queryClient } from "src/app/App";
import { useAppSelector } from "src/customHooks/useAppSelector";
import profileService from "src/services/profile.service";
import { showToast } from "src/utils/toast";

interface Props {
  employeeDisplayPicURL?: string;
  isEmployeeSearch?: boolean;
}

function ProfilePhoto({ employeeDisplayPicURL, isEmployeeSearch = false }: Props) {
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const [avatarURL, setAvatarURL] = useState<string | null>(null);

  const updatedPic = useMemo(() => {
    if (isEmployeeSearch) {
      return employeeDisplayPicURL || userDetails?.display_pic || null;
    }
    return userDetails?.display_pic || null;
  }, [employeeDisplayPicURL, isEmployeeSearch, userDetails?.display_pic]);

  useEffect(() => {
    setAvatarURL(updatedPic);
  }, [userDetails, employeeDisplayPicURL]);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) {
      return;
    }

    const file = event.target.files[0];
    try {
      const uploadedDoc = await uploadProfilePic([file], "Photo");
      if (uploadedDoc?.s3_link) {
        await profileService.updateProfilePicture(uploadedDoc.s3_link as string);
        queryClient.refetchQueries(["employee-details"]);
      }
    } catch (_err) {
      showToast("Failed to update profile picture", { type: "error" });
    }
  };

  return (
    <Box position="relative" display="inline-block">
      <Avatar src={avatarURL || ""} sx={{ width: 100, height: 100 }} />
      {!isEmployeeSearch && (
        <Tooltip title="Edit Avatar">
          <IconButton
            component="label"
            sx={{
              position: "absolute",
              bottom: 0,
              right: 0,
              backgroundColor: "white",
              "&hover": {
                backgroundColor: "light-gray",
              },
            }}
          >
            <EditIcon
              sx={{
                width: 20,
                height: 20,
              }}
            />
            <input
              id="profile-photo-input"
              type="file"
              accept="image/*"
              hidden
              onChange={handleFileChange}
              onClick={(e) => {
                (e.target as HTMLInputElement).value = "";
              }}
            />
          </IconButton>
        </Tooltip>
      )}
    </Box>
  );
}

export default ProfilePhoto;
