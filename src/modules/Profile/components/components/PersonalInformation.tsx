import { Box, Grid, Typography } from "@mui/material";
import React, { useMemo } from "react";
import { ViewProps } from "src/modules/Profile";
import { keyToTitleTransformer } from "src/modules/Profile/utils/keyToTitleTransformer";

export interface PersonalInformationMetaData {
  gender: string;
  dateOfBirth: string;
  mobileNumber: string;
  UAN_Number: string;
  PAN: string;
  phone: string;
  aadhaarNumber: string;
  nationality: string;
  bloodGroup: string;
  maritalStatus: string;
  address: string;
  passport: string;
}

interface PersonalInformationProps extends ViewProps<PersonalInformationMetaData> {}

const PersonalInformation: React.FC<PersonalInformationProps> = ({ informationMetaData, isViewMode }) => {
  const keyValueList = useMemo(() => {
    const items = Object.entries(informationMetaData);
    if (isViewMode) {
      return items.filter(([_, value]) => value && value !== "-");
    }
    return items;
  }, [informationMetaData, isViewMode]);

  return (
    <Box minHeight={260}>
      <Grid container spacing={2}>
        {keyValueList.map(([key, value]) => (
          <Grid item xs={4} sm={12} md={6} key={key} sx={{ gap: 1, display: "flex", flexDirection: "column" }}>
            <Typography variant="body2" color="#667085">
              {keyToTitleTransformer(key)}
            </Typography>
            <Typography sx={{ fontWeight: 500 }} variant="body2" color="#000000">
              {value}
            </Typography>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default PersonalInformation;
