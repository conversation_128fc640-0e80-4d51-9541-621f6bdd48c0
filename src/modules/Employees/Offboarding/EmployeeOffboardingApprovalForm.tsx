import { Box, Button, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React from "react";

import { BaseObject } from "src/app/global";
import Modal from "src/modules/Common/Modal/Modal";
import Span from "src/modules/Common/Span/Span";
import employeesService from "src/services/employees.service";
import validators, { ValidatorReturnType } from "src/utils/validators";

import { isBeforeToday } from "src/utils/dateUtils";
import { ValidationError } from "src/utils/errors";
import { ActionComponentProps, CommonFormWithState } from "../components/CommonForm";
import { FormDataType, FormInputType } from "../types/FormDataTypes";
import { convertListToOptions } from "../utils/utils";

export const containerStyle = {
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  gap: 4,
  borderRadius: "10px",
  border: "1px solid #EDEDED",
  padding: "20px",
};

export const cancelButtonStyle = {
  backgroundColor: "#EFF4F8",
  color: "#007F6F",
  height: "40px",
};

export const applyButtonStyle = {
  height: "40px",
};

const INPUT_FIELDS = {
  STATUS: "status",
  WAVE_OFF_NOTICE_PERIOD: "wave_off_notice_period",
  APPROVED_LAST_WORKING_DATE: "approved_last_working_date",
  APPROVED_LAST_WORKING_DATE_MIN_DATE: "approved_last_working_date_min_date",
  SALARY_ON_HOLD: "salary_on_hold",
  COMMENTS: "comments",
};

const customLastWorkingDateValidation = (value: string | number) => {
  //compare if the value is less than the last working date
  if (!value || isBeforeToday(value as string)) {
    return new ValidationError("required", "Approved last working date cannot be in the past");
  }
  return null;
};

const employeeOffboardingApprovalFormValidators = {
  [INPUT_FIELDS.STATUS]: [validators.validateInput],
  [INPUT_FIELDS.APPROVED_LAST_WORKING_DATE]: [customLastWorkingDateValidation],
  [INPUT_FIELDS.SALARY_ON_HOLD]: [validators.validateInput],
  [INPUT_FIELDS.COMMENTS]: [validators.validateInput],
};

const employeeOffboardingRejectionFormValidators = {
  [INPUT_FIELDS.STATUS]: [validators.validateInput],
  [INPUT_FIELDS.COMMENTS]: [validators.validateInput],
};

const employeeOffboardingApprovalForm: FormInputType[] = [
  {
    name: INPUT_FIELDS.STATUS,
    label: "Status",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.WAVE_OFF_NOTICE_PERIOD,
    label: "Waive off Notice Period",
    variant: "switch",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.APPROVED_LAST_WORKING_DATE,
    label: "Approved Last Working Day",
    variant: "date",
    minDate: INPUT_FIELDS.APPROVED_LAST_WORKING_DATE_MIN_DATE,
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.SALARY_ON_HOLD,
    label: "Salary On-hold",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.COMMENTS,
    label: "Comments",
    variant: "text",
    isRequired: true,
    xs: 12,
    rows: 3,
  },
];

const employeeRejectionForm: FormInputType[] = [
  {
    name: INPUT_FIELDS.STATUS,
    label: "Status",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.COMMENTS,
    label: "Comments",
    variant: "text",
    isRequired: true,
    xs: 12,
    rows: 3,
  },
];

const ActionComponent = ({ areFormDetailsValid, onSubmit, formDetails }: ActionComponentProps) => {
  const [showConfirmationModal, setShowConfirmationModal] = React.useState(false);
  const status = (formDetails as FormDataType)[INPUT_FIELDS.STATUS] as string;
  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          gap: 2,
          width: "100%",
          marginTop: "auto",
        }}
      >
        <Button
          variant="contained"
          sx={applyButtonStyle}
          disabled={!areFormDetailsValid}
          onClick={() => setShowConfirmationModal(true)}
        >
          Submit
        </Button>
      </Box>
      <Modal isOpen={showConfirmationModal} onClose={() => setShowConfirmationModal(false)} maxWidth="630px">
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 1,
            padding: "8px",
          }}
        >
          <Span sx={{ fontSize: "20px" }}>{status} Resignation request</Span>
          <Span sx={{ fontSize: "14px" }}>Are you sure you want to {status.toLowerCase()} resignation request?</Span>
          <Box display="flex" justifyContent="flex-end" sx={{ gap: 2, marginTop: "36px" }}>
            <Button variant="text" sx={cancelButtonStyle} onClick={() => setShowConfirmationModal(false)}>
              Cancel
            </Button>
            <Button variant="contained" sx={applyButtonStyle} onClick={onSubmit}>
              {status}
            </Button>
          </Box>
        </Box>
      </Modal>
    </>
  );
};

const employeeOffboardingApprovalPayload = (
  employee_code: string,
  data: FormDataType,
  isHRAdminView: boolean = false,
) => {
  return {
    employee_code,
    status: data[INPUT_FIELDS.STATUS] === "Accept" ? "Accepted" : "Denied",
    notice_period_waived_off: data[INPUT_FIELDS.WAVE_OFF_NOTICE_PERIOD],
    approved_last_working_date: data[INPUT_FIELDS.APPROVED_LAST_WORKING_DATE],
    salary_on_hold: data[INPUT_FIELDS.SALARY_ON_HOLD] === "Yes",
    comments: data[INPUT_FIELDS.COMMENTS],
    approver_role: isHRAdminView ? "HRBP" : "Manager",
  };
};

type EmployeeOffboardingApprovalFormProps = {
  title?: string;
  onClose: (isTerminated?: boolean) => void;
  employee_code: string;
  isHRAdminView?: boolean;
};

const selectOptions = {
  [INPUT_FIELDS.STATUS]: convertListToOptions(["Accept", "Deny"]),
  [INPUT_FIELDS.SALARY_ON_HOLD]: convertListToOptions(["Yes", "No"]),
};

const EmployeeOffboardingApprovalForm = ({
  employee_code,
  onClose,
  title,
  isHRAdminView = false,
}: EmployeeOffboardingApprovalFormProps) => {
  const mutation = useMutation({
    mutationKey: ["offboarding-approval"],
    mutationFn: async (employeeData: BaseObject) => employeesService.approveEmployeeOffboarding(employeeData),
    onSuccess: (response) => {
      if (response) onClose(true);
    },
  });
  const [form, setForm] = React.useState<FormInputType[]>(employeeOffboardingApprovalForm);
  const [validators, setValidators] = React.useState<{
    [x: string]: ((value: string | number) => ValidatorReturnType)[];
  }>(employeeOffboardingApprovalFormValidators);

  const approveEmployeeOffboarding = (form: FormDataType) => {
    mutation.mutate(employeeOffboardingApprovalPayload(employee_code, form, isHRAdminView));
  };

  const handleFormChange = (name: string, value: unknown, setFormDetail: (name: string, value: unknown) => void) => {
    if (name === INPUT_FIELDS.STATUS) {
      switch (value) {
        case "Deny":
          setForm(employeeRejectionForm);
          setValidators(employeeOffboardingRejectionFormValidators);
          Promise.resolve().then(() => {
            setFormDetail(INPUT_FIELDS.STATUS, value);
          });
          break;
        case "Accept":
          setForm(employeeOffboardingApprovalForm);
          setValidators(employeeOffboardingApprovalFormValidators);
          Promise.resolve().then(() => {
            setFormDetail(INPUT_FIELDS.STATUS, value);
          });
          break;
        default:
          break;
      }
    }
  };

  const employeeOffboardingApprovalFormInitialState = {
    [INPUT_FIELDS.STATUS]: "",
    [INPUT_FIELDS.WAVE_OFF_NOTICE_PERIOD]: false,
    [INPUT_FIELDS.APPROVED_LAST_WORKING_DATE]: "",
    [INPUT_FIELDS.APPROVED_LAST_WORKING_DATE_MIN_DATE]: new Date(),
    [INPUT_FIELDS.SALARY_ON_HOLD]: "",
    [INPUT_FIELDS.COMMENTS]: "",
  };

  return (
    <Box>
      {title && (
        <Typography variant="body1" fontWeight="400" color="text.primary" marginBottom="16px">
          {title}
        </Typography>
      )}
      <Box sx={containerStyle} padding="0">
        <CommonFormWithState
          inputElements={form}
          onFormSubmit={(form) => approveEmployeeOffboarding(form as FormDataType)}
          initialState={employeeOffboardingApprovalFormInitialState}
          validators={validators}
          ActionComponent={(props) => <ActionComponent {...props} />}
          gridStyles={{ rowSpacing: 2, gridSize: 8 }}
          selectOptions={selectOptions}
          onChange={handleFormChange}
        />
      </Box>
    </Box>
  );
};

export default EmployeeOffboardingApprovalForm;
