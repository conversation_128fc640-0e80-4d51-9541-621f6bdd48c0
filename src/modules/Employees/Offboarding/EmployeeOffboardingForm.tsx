import { ArrowBack } from "@mui/icons-material";
import { Box, Button, IconButton } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import { formatDate } from "date-fns";
import React, { useMemo } from "react";
import { BaseObject } from "src/app/global";
import Modal from "src/modules/Common/Modal/Modal";
import Span from "src/modules/Common/Span/Span";
import { EmployeeOffboardingDetailsResposnse } from "src/services/api_definitions/employees";
import employeesService from "src/services/employees.service";
import { isBeforeToday } from "src/utils/dateUtils";
import { ValidationError } from "src/utils/errors";
import validators from "src/utils/validators";
import { containerStyle } from "../EmployeeModalStyles";
import { ActionComponentProps, CommonFormWithState } from "../components/CommonForm";
import { FormDataType, FormInputType } from "../types/FormDataTypes";
import { convertListToOptions, getEnumValues } from "../utils/utils";

const acceptButtonStyle = {
  marginTop: "50px",
  padding: "10px 30px",
};

const cancelButtonStyle = {
  backgroundColor: "#EFF4F8",
  color: "#007F6F",
  height: "40px",
};

const applyButtonStyle = {
  height: "40px",
};

const INPUT_FIELDS = {
  APPROVER_NAME: "approver_name",
  NOTICE_PERIOD: "notice_period",
  LAST_WORKING_DATE: "last_working_date",
  DESIRED_LAST_WORKING_DATE: "desired_last_working_date",
  DESIRED_LAST_WORKING_DATE_MIN_DATE: "desired_last_working_date_min_date",
  REASON: "reason",
  DESCRIPTION: "description",
};

const customLastWorkingDateValidation = (value: string | number) => {
  //compare if the value is less than the last working date
  if (!value || isBeforeToday(value as string)) {
    return new ValidationError("required", "Approved last working date cannot be in the past");
  }
  return null;
};

const employeeSeparationFormValidators = {
  [INPUT_FIELDS.REASON]: [validators.validateInput],
  [INPUT_FIELDS.DESCRIPTION]: [validators.validateInput],
  [INPUT_FIELDS.DESIRED_LAST_WORKING_DATE]: [customLastWorkingDateValidation],
};

const employeeSeparationForm: FormInputType[] = [
  {
    name: INPUT_FIELDS.APPROVER_NAME,
    label: "Approver Name",
    variant: "text",
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.NOTICE_PERIOD,
    label: "Notice Period ",
    variant: "text",
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.LAST_WORKING_DATE,
    label: "Last Working Date",
    variant: "date",
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.DESIRED_LAST_WORKING_DATE,
    label: "Desired Last Working Date",
    variant: "date",
    isRequired: true,
    minDate: INPUT_FIELDS.DESIRED_LAST_WORKING_DATE_MIN_DATE,
  },
  {
    name: INPUT_FIELDS.REASON,
    label: "Separation Reason",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.DESCRIPTION,
    label: "Description",
    variant: "text",
    isRequired: true,
    xs: 12,
    rows: 3,
  },
];

type TerminationGuidlinesProps = {
  onContinue: () => void;
};

type EmployeeTerminationFormProps = {
  onClose: () => void;
  onSubmit: (form: FormDataType) => void;
  employeeOffboardingDetails: EmployeeOffboardingDetailsResposnse;
};

type ActionComponentOtherProps = {
  onCancel?: () => void;
};

type EmployeeTerminationProps = {
  employeeOffboardingDetails: EmployeeOffboardingDetailsResposnse;
  onClose: (isTerminated?: boolean) => void;
};

const EmployeeSeprationInfo = ({ onContinue }: TerminationGuidlinesProps) => {
  return (
    <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", width: "100%", padding: "36px" }}>
      <Span sx={{ fontSize: "34px", fontWeight: 500 }}>Employee separation</Span>
      <Span sx={{ fontSize: "18px", marginTop: "20px", lineHeight: "24px", textAlign: "center" }}>
        This process involves several steps, including notifying relevant parties, completing necessary documentations,
        conducting exit interviews, and ensuring the return of company's property.
      </Span>
      <Button variant="contained" sx={acceptButtonStyle} onClick={onContinue}>
        Initiate
      </Button>
    </Box>
  );
};

const ActionComponent = ({
  areFormDetailsValid,
  onSubmit,
  onCancel,
}: ActionComponentProps & ActionComponentOtherProps) => {
  const [showConfirmationModal, setShowConfirmationModal] = React.useState(false);
  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          gap: 2,
          width: "100%",
          marginTop: "auto",
          marginBottom: "16px",
        }}
      >
        <Button variant="text" sx={cancelButtonStyle} onClick={onCancel}>
          Cancel
        </Button>
        <Button
          variant="contained"
          sx={applyButtonStyle}
          disabled={!areFormDetailsValid}
          onClick={() => setShowConfirmationModal(true)}
        >
          Apply
        </Button>
      </Box>
      <Modal isOpen={showConfirmationModal} onClose={() => setShowConfirmationModal(false)} maxWidth="630px">
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 1,
            padding: "8px",
          }}
        >
          <Span sx={{ fontSize: "20px" }}>Submit resignation request</Span>
          <Span sx={{ fontSize: "14px" }}>Are you sure you want to submit resignation request?</Span>
          <Box display="flex" justifyContent="flex-end" sx={{ gap: 2, marginTop: "36px" }}>
            <Button variant="text" sx={cancelButtonStyle} onClick={() => setShowConfirmationModal(false)}>
              Cancel
            </Button>
            <Button variant="contained" sx={applyButtonStyle} onClick={onSubmit}>
              Apply
            </Button>
          </Box>
        </Box>
      </Modal>
    </>
  );
};

const terminateEmployeePayload = (data: FormDataType) => {
  return {
    desired_last_working_day: data[INPUT_FIELDS.DESIRED_LAST_WORKING_DATE],
    separation_reason: data[INPUT_FIELDS.REASON],
    description: data[INPUT_FIELDS.DESCRIPTION],
  };
};

const disabledInputFields = {
  [INPUT_FIELDS.APPROVER_NAME]: true,
  [INPUT_FIELDS.NOTICE_PERIOD]: true,
  [INPUT_FIELDS.LAST_WORKING_DATE]: true,
};

const EmployeeSeprationForm = ({ onClose, onSubmit, employeeOffboardingDetails }: EmployeeTerminationFormProps) => {
  const { data: seprationReasonList, isLoading } = getEnumValues("SeparationReason");

  const employeeSeparationFormInitialState = useMemo(() => {
    return {
      [INPUT_FIELDS.APPROVER_NAME]: employeeOffboardingDetails?.approver_name || "",
      [INPUT_FIELDS.NOTICE_PERIOD]: employeeOffboardingDetails?.notice_period || "",
      [INPUT_FIELDS.LAST_WORKING_DATE]: formatDate(employeeOffboardingDetails?.last_working_date, "yyyy-MM-dd") || "",
      [INPUT_FIELDS.DESIRED_LAST_WORKING_DATE]: "",
      [INPUT_FIELDS.DESIRED_LAST_WORKING_DATE_MIN_DATE]: new Date(),
      [INPUT_FIELDS.REASON]: "",
      [INPUT_FIELDS.DESCRIPTION]: "",
    };
  }, [employeeOffboardingDetails]);

  const selectOptions = {
    [INPUT_FIELDS.REASON]: isLoading ? [] : convertListToOptions(seprationReasonList as string[]),
  };

  return (
    <Box display="flex" flexDirection="column" width="100%" padding="6px" height="100%">
      <Box sx={{ borderBottom: "1px solid #E0E0E0", paddingBottom: "10px", marginBottom: "30px" }}>
        <IconButton onClick={onClose}>
          <ArrowBack sx={{ color: "#00000" }} />
        </IconButton>{" "}
        Back
      </Box>
      <CommonFormWithState
        inputElements={employeeSeparationForm}
        onFormSubmit={(form) => onSubmit(form as FormDataType)}
        initialState={employeeSeparationFormInitialState}
        validators={employeeSeparationFormValidators}
        ActionComponent={(props) => <ActionComponent onCancel={onClose} {...props} />}
        gridStyles={{ rowSpacing: 4, gridSize: 4, columnSpacing: 6 }}
        disabledInputFields={disabledInputFields}
        selectOptions={selectOptions}
      />
    </Box>
  );
};

const EmployeeOffboardingForm = ({ onClose, employeeOffboardingDetails }: EmployeeTerminationProps) => {
  const [showSeprationForm, setShowSeprationForm] = React.useState(false);
  const mutation = useMutation({
    mutationKey: ["employee-separation"],
    mutationFn: async (employeeData: BaseObject) => employeesService.initiateOffboarding(employeeData),
    onSuccess: (response) => {
      if (response) onClose(true);
    },
  });

  const initiateOffboarding = (form: FormDataType) => {
    mutation.mutate(terminateEmployeePayload(form));
  };

  return (
    <Box sx={containerStyle} padding="0">
      {!showSeprationForm ? (
        <EmployeeSeprationInfo onContinue={() => setShowSeprationForm(true)} />
      ) : (
        <EmployeeSeprationForm
          employeeOffboardingDetails={employeeOffboardingDetails}
          onClose={() => setShowSeprationForm(false)}
          onSubmit={initiateOffboarding}
        />
      )}
    </Box>
  );
};

export default EmployeeOffboardingForm;
