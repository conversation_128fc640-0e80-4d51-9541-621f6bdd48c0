import { ArrowBack } from "@mui/icons-material";
import { Box, Grid, IconButton } from "@mui/material";
import React, { useMemo } from "react";

import { format } from "date-fns";
import { EmployeeOffboardingTableResposnse, RequestStatus } from "src/services/api_definitions/employees";
import { DD_MM_YYYY, getIntlTimeToSpecifiedFormat } from "src/utils/dateUtils";
import DetailsSummaryCard from "./DetailsSummaryCard";
import EmployeeOffboardingApprovalForm from "./EmployeeOffboardingApprovalForm";
import EmployeeOffboardingStatus from "./EmployeeOffboardingStatus";

type Props = {
  employee: EmployeeOffboardingTableResposnse;
  onClose: () => void;
  onFormSubmit?: () => void;
  isManagerView?: boolean;
  isHRAdminView?: boolean;
};

const getOffboardingData = (employeeOffboardingDetails: EmployeeOffboardingTableResposnse) => {
  const managerResponse = employeeOffboardingDetails.request_status.find(
    (status) => status.approver_role === "Manager",
  );
  const HRAdminResponse = employeeOffboardingDetails.request_status.find((status) => status.approver_role === "HRBP");

  const getActionedAtColumnDef = (response: RequestStatus) => {
    if (response?.status === "Accepted") {
      return {
        label: "Accepted At",
        value: response?.actioned_at
          ? getIntlTimeToSpecifiedFormat(response?.actioned_at, DD_MM_YYYY).formattedDate
          : null,
      };
    }

    if (response?.status === "Denied") {
      return {
        label: "Denied At",
        value: response?.actioned_at
          ? getIntlTimeToSpecifiedFormat(response?.actioned_at, DD_MM_YYYY).formattedDate
          : null,
      };
    }
    return undefined;
  };
  return [
    [
      {
        label: "Employee Name",
        value: employeeOffboardingDetails?.name,
      },
      {
        label: "Designation",
        value: employeeOffboardingDetails?.job_title,
      },
      {
        label: "Department",
        value: employeeOffboardingDetails?.department,
      },
      {
        label: "Employee ID",
        value: employeeOffboardingDetails?.employee_code,
      },
      {
        label: "Resignation Date",
        value: format(employeeOffboardingDetails?.resignation_date, DD_MM_YYYY),
      },
      {
        label: "Last Working Day",
        value: format(employeeOffboardingDetails?.last_working_date, DD_MM_YYYY),
      },
      {
        label: "Desired Last Working Date",
        value: format(employeeOffboardingDetails?.desired_last_working_date, DD_MM_YYYY),
      },
      {
        label: "Separation Reason",
        value: employeeOffboardingDetails?.separation_reason,
      },
      {
        label: "Description",
        value: employeeOffboardingDetails?.description,
        xs: 12,
      },
    ],
    [
      {
        label: "Approved Last Working Date",
        value: managerResponse?.approved_last_working_date
          ? format(managerResponse?.approved_last_working_date, DD_MM_YYYY)
          : "",
      },
      {
        label: "Salary On-hold",
        value: managerResponse?.salary_on_hold ? "Yes" : "No",
      },
      {
        label: "Wave off Notice Period",
        value: managerResponse?.notice_period_waived_off ? "Yes" : "No",
      },
      getActionedAtColumnDef(managerResponse as RequestStatus),
    ],
    [
      {
        label: "Approved Last Working Date",
        value: HRAdminResponse?.approved_last_working_date
          ? format(HRAdminResponse?.approved_last_working_date, DD_MM_YYYY)
          : "",
      },
      {
        label: "Salary On-hold",
        value: HRAdminResponse?.salary_on_hold ? "Yes" : "No",
      },
      {
        label: "Wave off Notice Period",
        value: HRAdminResponse?.notice_period_waived_off ? "Yes" : "No",
      },
      getActionedAtColumnDef(HRAdminResponse as RequestStatus),
    ],
  ];
};

const getOffboardingStatus = (employeeOffboardingDetails: EmployeeOffboardingTableResposnse) => {
  const managerResponse = employeeOffboardingDetails.request_status.find(
    (status) => status.approver_role === "Manager",
  );
  const HRAdminResponse = employeeOffboardingDetails.request_status.find((status) => status.approver_role === "HRBP");
  return [
    {
      title: "Manager Response",
      status: managerResponse?.status || "",
      comment: managerResponse?.comments || "",
    },
    {
      title: "HR Response",
      status: HRAdminResponse?.status || "",
      comment: HRAdminResponse?.comments || "",
    },
  ];
};
const submittedStatus = ["Accepted", "Denied"];

const EmployeeOffboardingApprovalDetails = ({
  employee,
  onClose,
  onFormSubmit,
  isManagerView,
  isHRAdminView,
}: Props) => {
  const [[employeeData, managerData, HRAdminData], offboardingStatus] = useMemo(() => {
    return [getOffboardingData(employee), getOffboardingStatus(employee)];
  }, [employee]);

  if (!employee) {
    return null;
  }

  const onFormSubmitSuccess = (success?: boolean) => {
    if (success) {
      onFormSubmit?.();
      onClose();
    }
  };

  const isRequestDenied = employee.status === "Denied";
  const hasManagerResponse = submittedStatus.includes(offboardingStatus[0].status);
  const hasHRResponse = submittedStatus.includes(offboardingStatus[1].status);
  const isEmployeeHasRescind = employee.status === "Rescinded";

  return (
    <Box display="flex" flexDirection="column" width="100%" paddingTop="0" padding="6px" height="100%">
      <Box sx={{ borderBottom: "1px solid #E0E0E0", paddingBottom: "10px", marginBottom: "30px" }}>
        <IconButton onClick={onClose}>
          <ArrowBack sx={{ color: "#00000" }} />
        </IconButton>{" "}
        Back
      </Box>
      <Grid container columnSpacing="24" paddingBottom="30px">
        <Grid item xs={6}>
          <DetailsSummaryCard title="Employee Details" data={employeeData} />
          {hasManagerResponse && !isRequestDenied && (
            <Box marginTop="20px">
              <DetailsSummaryCard title="Manager Response" data={managerData} />
            </Box>
          )}
          {hasHRResponse && !isRequestDenied && (
            <Box marginTop="20px">
              <DetailsSummaryCard title="HR Response" data={HRAdminData} />
            </Box>
          )}
          {!isEmployeeHasRescind && (
            <Box marginTop="20px">
              {!hasManagerResponse && isManagerView && (
                <EmployeeOffboardingApprovalForm
                  employee_code={employee.employee_code}
                  onClose={onFormSubmitSuccess}
                  title="Manager response"
                />
              )}
              {!hasHRResponse && !isRequestDenied && isHRAdminView && (
                <EmployeeOffboardingApprovalForm
                  employee_code={employee.employee_code}
                  onClose={onFormSubmitSuccess}
                  title="HR response"
                  isHRAdminView
                />
              )}
            </Box>
          )}
        </Grid>
        <Grid item xs={6}>
          <EmployeeOffboardingStatus steps={offboardingStatus} title="Request Status" />
        </Grid>
      </Grid>
    </Box>
  );
};

export default EmployeeOffboardingApprovalDetails;
