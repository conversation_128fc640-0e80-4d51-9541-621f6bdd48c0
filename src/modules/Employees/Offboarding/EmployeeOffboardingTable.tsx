import { VisibilityOutlined } from "@mui/icons-material";
import { IconButton, Typography } from "@mui/material";
import { useQueries } from "@tanstack/react-query";
import { format } from "date-fns";
import { MRT_ColumnDef } from "material-react-table";
import React, { useImperativeHandle } from "react";

import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import LoadingScreen from "src/modules/Common/LoadingScreen/LoadingScreen";
import DataTable from "src/modules/Common/Table/DataTable";
import { EmployeeOffboardingTableResposnse } from "src/services/api_definitions/employees";
import employeesService from "src/services/employees.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";

const columns: MRT_ColumnDef<EmployeeOffboardingTableResposnse>[] = [
  {
    accessorKey: "name",
    header: "Employee",
    Cell: ({ row }) => (
      <EmployeeCellInfo
        name={row?.original?.name}
        jobTitle={row?.original?.job_title}
        displayPic={row?.original?.display_pic}
      />
    ),
    size: 220,
  },
  {
    accessorKey: "department",
    header: "Department",
    size: 180,
  },
  {
    accessorKey: "resignation_date",
    header: "Resignation Date",
    size: 180,
    Cell: ({ row }) => formatDateToDayMonthYear(row?.original?.resignation_date),
  },
  {
    accessorKey: "notice_period",
    header: "Notice Period",
    size: 180,
  },
  {
    accessorKey: "status",
    header: "Current Stage",
    Cell: ({ row }) => <Typography color={getStatusColors(row?.original?.status)}>{row?.original?.status}</Typography>,
    size: 180,
  },
  {
    accessorKey: "action",
    header: "Action",
  },
];

type EmployeeOffboardingTableProps = {
  onViewClick: (employee_offboarding_details: EmployeeOffboardingTableResposnse | null) => void;
  isHRAdminView?: boolean;
  refetchRef?: React.RefObject<{
    refetch?: () => void;
  }>;
};

const EmployeeOffboardingTable = ({ onViewClick, isHRAdminView, refetchRef }: EmployeeOffboardingTableProps) => {
  const result = useQueries({
    queries: [
      {
        queryKey: ["get-all-employee-offboarding-details"],
        queryFn: async () => employeesService.getAllEmployeeOffboardingDetails(isHRAdminView),
        retryOnMount: false,
        refetchOnWindowFocus: false,
      },
    ],
  });
  const [{ data: allEmployeeOffboardingDetails, isLoading, refetch }] = result;

  useImperativeHandle(refetchRef, () => ({
    refetch,
  }));

  const tableData = React.useMemo(() => {
    if (!allEmployeeOffboardingDetails) return [];
    return allEmployeeOffboardingDetails.map((request) => ({
      ...request,
      resignation_date: format(request.resignation_date, "yyyy-MM-dd"),
      action: (
        <IconButton onClick={() => onViewClick(request)}>
          <VisibilityOutlined />
        </IconButton>
      ),
    }));
  }, [allEmployeeOffboardingDetails]);

  if (!isLoading && allEmployeeOffboardingDetails) {
    return <DataTable columns={columns} data={tableData} />;
  }

  return <LoadingScreen />;
};

export default EmployeeOffboardingTable;
