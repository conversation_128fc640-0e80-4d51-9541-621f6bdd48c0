import CircleIcon from "@mui/icons-material/Circle";
import { Box, Typography, styled } from "@mui/material";
import Step from "@mui/material/Step";
import StepConnector, { stepConnectorClasses } from "@mui/material/StepConnector";
import StepLabel from "@mui/material/StepLabel";
import Stepper from "@mui/material/Stepper";
import React from "react";
import { getStatusColors } from "src/utils/typographyUtils";

const containerStyles = {
  display: "flex",
  width: "100%",
  background: "rgba(249, 249, 249, 1)",
  borderRadius: "10px",
  padding: "20px",
};

const activeIconStyles = {
  backgroundColor: "rgba(87, 186, 87, 0.7)",
  borderRadius: "50%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  width: 24,
  height: 24,
};

const disableIconStyles = {
  width: 24,
  height: 24,
  borderRadius: "50%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
};

const labelStyle = {
  color: "rgba(102, 112, 133, 1)",
  fontSize: "14px",
};

const valueStyle = {
  color: "#000000",
  marginLeft: "16px",
  fontSize: "14px",
};

export const CustomConnector = styled(StepConnector)(() => ({
  position: "absolute",
  top: "24px",
  bottom: "-16px",
  left: "-1px",
  [`&.${stepConnectorClasses.vertical}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      height: "100%",
    },
  },
  [`&.${stepConnectorClasses.active}`]: {
    top: "32px",
    [`& .${stepConnectorClasses.line}`]: {},
  },
  [`&.${stepConnectorClasses.completed}`]: {
    top: "32px",
    bottom: "-8px",
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: "#57ba57",
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    borderColor: "#eaeaf0",
    borderWidth: 2,
  },
  [`&.${stepConnectorClasses.disabled}`]: {
    display: "none",
  },
}));

const stepConnecterStyle = {
  width: "100%",
  ".MuiStepLabel-iconContainer": {
    alignSelf: "flex-start",
  },
};

export type Steps = {
  title: string;
  status: string;
  comment: string;
}[];

type Props = {
  steps: Steps;
  title?: string;
};

const stepStyle = {
  display: "flex",
  position: "relative",
  width: "100%",
  ".MuiStep-vertical": {
    width: "100%",
  },
  "&.Denied": {
    [`.${stepConnectorClasses.line}`]: {
      borderColor: "#FF4C4C !important",
    },
  },
  "&.Rescinded": {
    [`.${stepConnectorClasses.line}`]: {
      borderColor: "#FF4C4C !important",
    },
  },
};

const getStatusStyle = (status: string) => {
  switch (status) {
    case "Accepted":
      return activeIconStyles;
    case "Rescinded":
    case "Denied":
      return {
        ...activeIconStyles,
        backgroundColor: "rgba(255, 76, 76, 0.7)",
      };
    case "Pending":
      return {
        ...activeIconStyles,
        backgroundColor: "rgba(255, 165, 0, 0.7)",
      };
    default:
      return activeIconStyles;
  }
};

const getStatusTextStyle = (status: string) => {
  switch (status) {
    case "Accepted":
      return {
        ...valueStyle,
        color: "#57BA57",
      };
    case "Rescinded":
    case "Denied":
      return {
        ...valueStyle,
        color: "#FF4C4C",
      };
    case "Pending":
      return {
        ...valueStyle,
        color: "#FFA500",
      };
    default:
      return valueStyle;
  }
};

const EmployeeOffboardingStatus = ({ steps, title }: Props) => {
  const pendingStatusIndex = steps.findIndex(({ status }) => status === "Pending");
  const activeStep = pendingStatusIndex === -1 ? steps.length - 1 : pendingStatusIndex;
  return (
    <Box>
      {title && (
        <Typography variant="body1" fontWeight="400" color="text.primary" marginBottom="16px">
          {title}
        </Typography>
      )}
      <Box sx={containerStyles}>
        <Box sx={{ display: "flex", width: "100%" }}>
          <Stepper
            sx={{ width: "100%" }}
            orientation="vertical"
            activeStep={activeStep}
            connector={<CustomConnector />}
          >
            {steps.map(({ title, status, comment }, index) => {
              return (
                <Box sx={stepStyle} key={title} className={status}>
                  <Step
                    key={title}
                    active={index === activeStep}
                    completed={index < activeStep}
                    disabled={index === steps.length - 1}
                  >
                    <StepLabel
                      sx={stepConnecterStyle}
                      icon={
                        <Box sx={index <= activeStep ? getStatusStyle(status) : disableIconStyles}>
                          <CircleIcon
                            sx={{
                              width: 12,
                              height: 12,
                              color: index <= activeStep ? getStatusColors(status) : "#D2D2D2",
                            }}
                          />
                        </Box>
                      }
                    >
                      <Box
                        sx={{
                          marginLeft: "10px",
                          borderBottom: index < steps.length - 1 ? "1px solid rgba(230, 230, 230, 1)" : "none",
                          paddingBottom: "20px",
                        }}
                      >
                        <Typography variant="body1" fontWeight="400" color="#000000" marginBottom="4px">
                          {title}
                        </Typography>
                        <Box display="flex" marginTop="20px">
                          <Typography sx={labelStyle}>Action:</Typography>
                          <Typography sx={getStatusTextStyle(status)}>{status}</Typography>
                        </Box>
                        <Box display="flex" marginTop="20px">
                          <Typography sx={labelStyle}>Comments:</Typography>
                          <Typography sx={valueStyle}>{comment}</Typography>
                        </Box>
                      </Box>
                    </StepLabel>
                  </Step>
                </Box>
              );
            })}
          </Stepper>
        </Box>
      </Box>
    </Box>
  );
};

export default EmployeeOffboardingStatus;
