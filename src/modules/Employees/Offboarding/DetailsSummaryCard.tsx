import { Box, Grid, Typography } from "@mui/material";
import React from "react";

type DetailsSummaryCardProps = {
  data: {
    label: string;
    value?: string;
    xs?: number;
  }[];
  title?: string;
  variant?: string;
};

const containerStyle = {
  borderRadius: "10px",
  border: "1px solid #EDEDED",
};

const boxStyle = {
  display: "flex",
  justifyContent: "space-between",
  borderBottom: "1px solid #EDEDED",
  padding: "12px 16px",
  minHeight: "56px",
  "&:last-child": {
    borderBottom: "none",
  },
};

const valueV2Styles = {
  maxWidth: "60%",
};

const DetailsSummaryCard = ({ title, data, variant = "v1" }: DetailsSummaryCardProps) => {
  return (
    <Box>
      {title && (
        <Typography variant="body1" fontWeight="400" color="text.primary" marginBottom="16px">
          {title}
        </Typography>
      )}
      <Box sx={containerStyle}>
        {variant === "v1" ? (
          <Grid container padding="20px" rowGap="32px">
            {data.map(({ label, value, xs = 6 }) => (
              <Grid key={label} item xs={xs}>
                <Typography variant="body2" fontWeight="400" color="#667085" lineHeight="22px">
                  {label}
                </Typography>
                <Typography variant="body2" fontWeight="400" color="#000000" lineHeight="22px">
                  {value}
                </Typography>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Box>
            {data.map(({ label, value }) => (
              <Box key={label} sx={boxStyle}>
                <Typography variant="body2" fontWeight="400" color="#667085">
                  {label}
                </Typography>
                <Typography sx={valueV2Styles} variant="body2" fontWeight="400" color="#000000">
                  {value}
                </Typography>
              </Box>
            ))}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default DetailsSummaryCard;
