import { Box, Button, Grid } from "@mui/material";
import React from "react";
import { EmployeeOffboardingDetailsResposnse } from "src/services/api_definitions/employees";

import { useMutation } from "@tanstack/react-query";
import { format } from "date-fns/format";
import Modal from "src/modules/Common/Modal/Modal";
import Span from "src/modules/Common/Span/Span";
import employeesService from "src/services/employees.service";
import DetailsSummaryCard from "./DetailsSummaryCard";
import { applyButtonStyle, cancelButtonStyle } from "./EmployeeOffboardingApprovalForm";
import EmployeeOffboardingStatus from "./EmployeeOffboardingStatus";

type Props = {
  employeeOffboardingDetails: EmployeeOffboardingDetailsResposnse;
  refetch: () => void;
};

const rescindButtonStyle = {
  margin: "30px 0",
};

type ConfirmationModalProps = {
  showConfirmationModal: boolean;
  setShowConfirmationModal: React.Dispatch<React.SetStateAction<boolean>>;
  onSubmit: () => void;
};
const ConfirmationModal = ({ showConfirmationModal, setShowConfirmationModal, onSubmit }: ConfirmationModalProps) => {
  return (
    <Modal isOpen={showConfirmationModal} onClose={() => setShowConfirmationModal(false)} maxWidth="630px">
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 1,
          padding: "8px",
        }}
      >
        <Span sx={{ fontSize: "20px" }}>Rescind Resignation request</Span>
        <Span sx={{ fontSize: "14px" }}>Are you sure you want to rescind resignation request?</Span>
        <Box display="flex" justifyContent="flex-end" sx={{ gap: 2, marginTop: "36px" }}>
          <Button variant="text" sx={cancelButtonStyle} onClick={() => setShowConfirmationModal(false)}>
            Cancel
          </Button>
          <Button variant="contained" sx={applyButtonStyle} onClick={onSubmit}>
            Rescind
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

const EmployeeOffboardingDetails = ({ employeeOffboardingDetails, refetch }: Props) => {
  const [showConfirmationModal, setShowConfirmationModal] = React.useState(false);

  const mutation = useMutation({
    mutationKey: ["offboarding-rescind"],
    mutationFn: async () => employeesService.rescindEmployeeOffboarding(),
    onSuccess: (response) => {
      if (response) refetch();
    },
  });

  const offboardingDetails = [
    {
      label: "Approver Name",
      value: employeeOffboardingDetails.approver_name,
    },
    {
      label: "Resignation Date",
      value: format(employeeOffboardingDetails.resignation_date, "dd MMM yyyy"),
    },
    {
      label: "Desired Last Working Date",
      value: format(employeeOffboardingDetails.desired_last_working_date, "dd MMM yyyy"),
    },
    {
      label: "Separation Reason",
      value: employeeOffboardingDetails.separation_reason,
    },
    {
      label: "Description",
      value: employeeOffboardingDetails.description,
    },
    {
      label: "Approved Last Working Date",
      value: employeeOffboardingDetails.approved_last_working_date
        ? format(employeeOffboardingDetails.approved_last_working_date, "dd MMM yyyy")
        : "",
    },
    {
      label: "Salary On Hold",
      value:
        employeeOffboardingDetails.salary_on_hold != null
          ? employeeOffboardingDetails.salary_on_hold === true
            ? "Yes"
            : "No"
          : "",
    },
    {
      label: "Notice Period Waived Off",
      value:
        employeeOffboardingDetails.notice_period_waived_off != null
          ? employeeOffboardingDetails.notice_period_waived_off === true
            ? "Yes"
            : "No"
          : "",
    },
  ].filter((detail) => detail.value !== "");

  const managerResponse = employeeOffboardingDetails.request_status.find(
    (status) => status.approver_role === "Manager",
  );
  const hrResponse = employeeOffboardingDetails.request_status.find((status) => status.approver_role === "HRBP");
  const hasHRBPApproved = hrResponse?.status === "Accepted";

  const offboardingStatus = [
    {
      title: "Manager Response",
      status: managerResponse?.status || "",
      comment: managerResponse?.comments || "",
    },
    {
      title: "HR Response",
      status: hrResponse?.status || "",
      comment: hrResponse?.comments || "",
    },
  ];

  return (
    <Grid container columnSpacing="24">
      <Grid item xs={6}>
        <DetailsSummaryCard title="Employee Details" data={offboardingDetails} variant="v2" />
        {!hasHRBPApproved && (
          <Button sx={rescindButtonStyle} variant="contained" onClick={() => setShowConfirmationModal(true)}>
            Rescind
          </Button>
        )}
        <ConfirmationModal
          showConfirmationModal={showConfirmationModal}
          setShowConfirmationModal={setShowConfirmationModal}
          onSubmit={() => mutation.mutate()}
        />
      </Grid>
      <Grid item xs={6}>
        <EmployeeOffboardingStatus steps={offboardingStatus} title="Request Status" />
      </Grid>
    </Grid>
  );
};

export default EmployeeOffboardingDetails;
