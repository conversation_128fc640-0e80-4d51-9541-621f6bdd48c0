import { Box, Divider } from "@mui/material";
import React, { useEffect, useImperativeHandle } from "react";
import { useForm } from "src/customHooks/useForm";

import LoadingScreen from "../LoadingScreen";
import {
  INPUT_FIELDS,
  PersonalInformationFormValidators,
  PersonalInformationInitialValues,
  form,
  permanentAdressForm,
} from "../config/PersonalInformation";
import { FormDataType, FormDataTypeString, StepperComponentProps } from "../types/FormDataTypes";
import { convertListToOptions, getEnumValues } from "../utils/utils";
import AddressFormUnit from "./AdressUnitForm";
import { CommonForm } from "./CommonForm";

type Props = StepperComponentProps & {
  formData?: FormDataType[];
  onFormComplete: (form: FormDataType, isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
  disabledInputFields?: string[];
};

const requiredPermanentAddressFields = permanentAdressForm.filter(
  (field) => field.name !== INPUT_FIELDS.PERMANENT_STREET,
);
const PersonalInformationUnit = ({
  formData = [],
  onFormComplete,
  disabledInputFields,
  setDisableNext,
  formActionButton,
  isViewOnlyMode,
}: Props) => {
  const { formDetails, formErrors, setFormDetail, areFormDetailsValid, setFormErrors } = useForm({
    initialState: formData[0] || PersonalInformationInitialValues[0],
    validations: PersonalInformationFormValidators,
  });

  useEffect(() => {
    const isAnyPermanentAddressFilled = permanentAdressForm.some(
      (field) => !!(formDetails as FormDataTypeString)[field.name],
    );
    const isAllPermanentAddressFilled = requiredPermanentAddressFields.every(
      (field) => !!(formDetails as FormDataTypeString)[field.name],
    );
    if (isAnyPermanentAddressFilled && !isAllPermanentAddressFilled) {
      requiredPermanentAddressFields.forEach((field) => {
        if ((formDetails as FormDataTypeString)[field.name]) return;
        setFormErrors?.((prevErrors) => ({
          ...prevErrors,
          [field.name]: "Input should not be empty",
        }));
      });
    }
    if (!isAnyPermanentAddressFilled) {
      requiredPermanentAddressFields.forEach((field) => {
        setFormErrors?.((prevErrors) => ({
          ...prevErrors,
          [field.name]: "",
        }));
      });
    }
    const isFormDisabled = !areFormDetailsValid || (isAnyPermanentAddressFilled && !isAllPermanentAddressFilled);
    setDisableNext?.(isFormDisabled);
  }, [areFormDetailsValid, formDetails]);

  useImperativeHandle(formActionButton, () => ({
    next: (isFormSubmit, isSaveDraft) => {
      onFormComplete(formDetails as FormDataType, isFormSubmit, isSaveDraft);
    },
  }));

  const { data: genderList = [], isLoading: genderLoading } = getEnumValues("Gender");
  const { data: nationalityList = [], isLoading: nationalityLoading } = getEnumValues("Nationality");
  const { data: maritalStatusList = [], isLoading: maritalStatusLoading } = getEnumValues("MaritalStatus");
  const { data: bloodGroupList = [], isLoading: bloodGroupLoading } = getEnumValues("BloodGroup");

  if (genderLoading || nationalityLoading || bloodGroupLoading || maritalStatusLoading) {
    return <LoadingScreen />;
  }

  const selectOptions = {
    [INPUT_FIELDS.GENDER]: convertListToOptions(genderList),
    [INPUT_FIELDS.BLOOD_GROUP]: convertListToOptions(bloodGroupList),
    [INPUT_FIELDS.NATIONALITY]: convertListToOptions(nationalityList),
    [INPUT_FIELDS.MARITAL_STATUS]: convertListToOptions(maritalStatusList),
  };

  const disabledInputFieldsObject = disabledInputFields?.reduce(
    (acc, field) => {
      return { ...acc, [field]: true };
    },
    {} as Record<string, boolean>,
  );

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      <CommonForm
        onChange={setFormDetail}
        inputElements={form}
        selectOptions={selectOptions}
        isViewOnlyMode={isViewOnlyMode}
        formErrors={formErrors as Record<string, string>}
        formValues={formDetails as Record<string, unknown>}
        disabledInputFields={disabledInputFieldsObject}
      />
      <Divider sx={{ width: "100%", margin: "8px 0" }} />
      <AddressFormUnit
        onChange={setFormDetail}
        isViewOnlyMode={isViewOnlyMode}
        disabledInputFields={disabledInputFieldsObject}
        formErrors={formErrors as Record<string, string>}
        formDetails={formDetails as Record<string, unknown>}
      />
    </Box>
  );
};

export default PersonalInformationUnit;
