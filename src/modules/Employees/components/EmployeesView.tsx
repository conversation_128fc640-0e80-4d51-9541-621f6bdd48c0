import { MoreVert } from "@mui/icons-material";
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Box, CircularProgress, IconButton } from "@mui/material";
import { MRT_ColumnDef, MRT_VisibilityState } from "material-react-table";
import React, { Suspense, useCallback, useMemo } from "react";
import { Actions } from "src/app/constants";
import { createColumnConfig } from "src/configs/table/employees.table.config";
import { useMasterData } from "src/customHooks/useMasterData";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import DataTable from "src/modules/Common/Table/DataTable";
import {
  calculateTotalCTC,
  formatCurrency,
} from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import DeleteAttendanceConfig from "src/modules/TeamCalendar/components/DeleteAttendanceConfig";
import EditAttendanceConfig from "src/modules/TeamCalendar/components/EditAttendanceConfig";
import LeaveBalanceModal from "src/pages/LeaveBalanceModal";
import { EmployeeDetails, LeaveBalanceItems, TransformedEmployee } from "src/services/api_definitions/employees";
import { Country } from "src/services/api_definitions/location.service";
import EmployeeProfile from "../EmployeeProfile";
import EmployeeTermination from "../EmployeeTermination";
import ChangeDateOfConfirmation from "./ChangeDateOfConfirmation";
import EmploymentJourneyModal from "./EmploymentJourneyModal";
import EnableGoalSettings from "./EnableGoalSettings";
import SettingsMenu from "./SettingsMenu";

const InitialVisibilityState = {
  employee_code: true,
  employee_name: true,
  job_title: true,
  department: true,
  cost_center: false,
  reporting_manager: true,
  employee_status: true,
  employee_type: true,
  aadhar: false,
  blood_group: false,
  date_of_birth: false,
  date_of_confirmation: false,
  date_of_joining: false,
  email: true,
  gender: false,
  location: false,
  marital_status: false,
  nationality: false,
  organisation: false,
  pan: false,
  passport: false,
  personal_email: false,
  phone: false,
  uan: false,
  bank_account: false,
  current_address: false,
  permanent_address: false,
  emergency_contacts: false,
  reportees: false,
  "office_address.display_address": false,
  hrbpEmail: false,
  business_unit: false,
  compensations: false,
};

interface EmployeesViewProps {
  data: TransformedEmployee[];
  isLoading: boolean;
  isFetching: boolean;
  refetchEmployeeList?: () => void;
}

type SelectedOptionViewProps = {
  option: string;
  onClose: (isTerminated?: boolean) => void;
  employee: TransformedEmployee;
};

const convertLeaveBalance = (row: Record<string, number>): LeaveBalanceItems[] => {
  return Object.keys(row).map((key) => ({
    leave_type: key,
    count: row[key],
  }));
};

const SelectedOptionView = (props: SelectedOptionViewProps) => {
  switch (props.option) {
    case Actions.TERMINATE:
      return <EmployeeTermination isOpen {...props} />;
    case Actions.VIEW_PROFILE:
      return <EmployeeProfile isOpen {...props} isViewOnlyMode />;
    case Actions.EDIT_PROFILE:
      return <EmployeeProfile isOpen {...props} />;
    case Actions.CHANGE_DATE_OF_CONFIRMATION:
      return <ChangeDateOfConfirmation isOpen {...props} />;
    case Actions.VIEW_JOURNEY:
      return <EmploymentJourneyModal isOpen {...props} />;
    case Actions.EDIT_ATTENDANCE_CONFIG:
      return (
        <EditAttendanceConfig
          canReplicateForAll={false}
          displayName={props?.employee?.employee_name}
          employeeCode={props?.employee?.employee_code}
          {...props}
        />
      );
    case Actions.DELETE_ATTENDANCE_CONFIG:
      return (
        <DeleteAttendanceConfig
          canReplicateForAll={false}
          displayName={props?.employee?.employee_name}
          employeeCode={props?.employee?.employee_code}
          {...props}
        />
      );
    case Actions.ENABLE_GOAL_SETTING:
      return <EnableGoalSettings isOpen {...props} />;
    case Actions.VIEW_LEAVE_BALANCE:
      return (
        <LeaveBalanceModal
          isOpen={true}
          onClose={() => props.onClose()}
          leaveBalanceData={convertLeaveBalance(props.employee?.leave_balance)}
        />
      );
    default:
      return null;
  }
};

const EmployeesView: React.FC<EmployeesViewProps> = ({ data, isLoading, isFetching, refetchEmployeeList }) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [columnVisibility, setColumnVisibility] = React.useState<MRT_VisibilityState>(InitialVisibilityState);
  const [selectedOption, setSelectedOption] = React.useState<string | null>(null);
  const [currentEmployee, setCurrentEmployee] = React.useState<TransformedEmployee | null>(null);
  const { data: countries } = useMasterData<Country>("Country");

  const handleClick = useCallback((event: React.MouseEvent<HTMLElement>, employee: TransformedEmployee) => {
    setCurrentEmployee(employee);
    setAnchorEl(event.currentTarget);
  }, []);

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const columnCallbackMap = useMemo(
    () =>
      new Map([
        [
          "employee_name",
          (row: any) => {
            const avatarSrc = row?.originalData?.display_pic || "";
            return <EmployeeCellInfo name={row?.employee_name} displayPic={avatarSrc} />;
          },
        ],
        ["employment_status", (row: any) => row?.employment_status],
      ]),
    [],
  );

  const columnConfigs = useCallback(
    () =>
      [
        ...createColumnConfig(columnCallbackMap),
        {
          header: "CTC",
          accessorKey: "compensations",
          size: 250,
          accessorFn: (row) => {
            const currency =
              (row?.current_compensation?.components?.length || 0) > 0
                ? row?.current_compensation?.components?.[0]?.currency
                : "INR";
            const countryLocale =
              countries?.find((country) => country?.name === row?.office_address?.country)?.code || "IN";
            return row?.current_compensation?.components?.length > 0
              ? formatCurrency(calculateTotalCTC(row?.current_compensation?.components), currency, countryLocale)
              : "<Not Configured>";
          },
        },
      ] as MRT_ColumnDef<EmployeeDetails, unknown>[],
    [columnCallbackMap, countries],
  );

  return (
    <Box sx={{ mt: "18px" }}>
      <Suspense fallback={<CircularProgress sx={{ alignItems: "center" }} />}>
        <DataTable
          data={(data as unknown as EmployeeDetails[]) || []}
          columns={columnConfigs()}
          enablePagination={false}
          enableStickyHeader
          enableRowActions
          enableColumnPinning
          positionGlobalFilter="left"
          displayColumnDefOptions={{
            "mrt-row-actions": {
              header: "Actions",
              size: 50,
            },
          }}
          initialState={{
            columnPinning: {
              right: ["mrt-row-actions"],
              left: ["employee_code", "employee_name"],
            },
          }}
          renderRowActions={(row) => (
            <IconButton onClick={(e) => handleClick(e, row?.row?.original as unknown as TransformedEmployee)}>
              <MoreVert />
            </IconButton>
          )}
          onColumnVisibilityChange={setColumnVisibility}
          state={{
            showSkeletons: isLoading && isFetching,
            columnVisibility: columnVisibility,
          }}
          localization={{
            actions: "Settings",
          }}
          muiTopToolbarProps={() => ({
            sx: {
              "& .MuiBox-root": {
                gap: 0,
              },
            },
          })}
          enableColumnActions={true}
          enableCellActions={true}
          enableSorting={true}
          enableTopToolbar={true}
        />
      </Suspense>
      <SettingsMenu
        employee={currentEmployee}
        anchorEl={anchorEl}
        handleClose={handleClose}
        onOptionClick={(option) => setSelectedOption(option)}
      />
      {selectedOption && currentEmployee && (
        <SelectedOptionView
          employee={currentEmployee}
          option={selectedOption}
          onClose={(refetchList) => {
            if (refetchList) refetchEmployeeList?.();
            setSelectedOption(null);
          }}
        />
      )}
    </Box>
  );
};

export default EmployeesView;
