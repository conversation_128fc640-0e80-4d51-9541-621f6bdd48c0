import React from "react";
import CreateEmployeeModal from "../../CreateEmployeeModal";
import { ExportEmployeesModal } from "../ExportEmployeesModal";
import { ImportModal } from "./index";

type RenderModalProps = {
  open: boolean;
  modalId: string | null;
  handleClose: () => void;
};

const RenderModal: React.FC<RenderModalProps> = ({ open, modalId, handleClose }) => {
  switch (modalId) {
    case "import":
      return <ImportModal open={open} onClose={handleClose} />;
    case "add":
      return <CreateEmployeeModal open={open} onClose={handleClose} />;
    case "export":
      return <ExportEmployeesModal open={open} onClose={handleClose} />;
    default:
      return null;
  }
};

interface ModalControllerProps extends RenderModalProps {}

const ModalController: React.FC<ModalControllerProps> = ({ open, modalId, handleClose }) => (
  <RenderModal open={open} modalId={modalId} handleClose={handleClose} />
);

export default ModalController;
