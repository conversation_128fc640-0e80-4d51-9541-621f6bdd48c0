import { CloudDownload, Folder } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useState } from "react";
import languageConfig from "src/configs/language/en.lang";
import { useAppSelector } from "src/customHooks/useAppSelector";
import AlertsAccordion from "src/modules/Common/AlertsAccordion/AlertsAccordion";
import ButtonWithLoading from "src/modules/Common/ButtonWithLoading/ButtonWithLoading";
import FileDropzone, { FileDropVariants } from "src/modules/Common/FileDropzone/FileDropzone";
import Modal from "src/modules/Common/Modal/Modal";
import { FileUploadResponse } from "src/services/api_definitions/default.service";
import EmployeesServiceAPI from "src/services/employees.service";
import { ImportModalStyles, ModalControllerStyles } from "../styles/styles.module";

interface ImportModalProps {
  open: boolean;
  onClose: () => void;
}

const ImportModal: React.FC<ImportModalProps> = ({ open, onClose }) => {
  const [files, setFiles] = useState<File[] | null>(null);
  const [fileDropzoneVariant, setFileDropzoneVariant] = useState<FileDropVariants>("default");
  const [errorDetails, setErrorDetails] = useState<FileUploadResponse<string | string[]>>();
  const { status } = useAppSelector((state) => state.userManagement?.userDetails?.organisations?.[0]);

  const mutation = useMutation({
    mutationKey: ["download-sample-template"],
    mutationFn: async () => EmployeesServiceAPI.downloadSampleImportEmployeesTemplate(),
  });

  const fileImportMutation = useMutation({
    mutationKey: ["import-employees-details"],
    mutationFn: async (file: File) => EmployeesServiceAPI.uploadFile(file) as never,
    onSuccess: () => {
      setFileDropzoneVariant("success");
      if (status === "HR Onboarding") {
        window.location.reload();
      } else {
        onClose();
      }
    },
    onError: (error: FileUploadResponse<string | string[]>) => {
      setFileDropzoneVariant("error");
      setErrorDetails(error);
    },
  });

  const resetFileDropState = () => {
    setFileDropzoneVariant("default");
    setErrorDetails(undefined);
  };

  const onFileDrop = async <T extends File>(files: T[]) => {
    resetFileDropState();
    setFiles(files);
  };

  const onImportClick = async () => {
    if (files && files?.length > 0) fileImportMutation.mutate(files[0]);
  };

  const onDownloadSampleIconClick = () => {
    mutation.mutate();
  };

  return (
    <Modal
      isOpen={open}
      onClose={onClose}
      sx={ModalControllerStyles.root}
      PaperProps={{
        style: ModalControllerStyles.paper,
      }}
      title={languageConfig.employees.modals.import.title}
      subtitle={languageConfig.employees.modals.import.subtitle}
      showBackButton
      isLoading={fileImportMutation.isLoading}
    >
      <Box style={ImportModalStyles.root}>
        <Box sx={ImportModalStyles.body.container}>
          <FileDropzone
            variant={fileDropzoneVariant}
            files={files}
            width="100%"
            height={250}
            onFileDrop={onFileDrop}
            acceptFileTypes={{
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
            }}
            message={typeof errorDetails?.message === "string" ? errorDetails.message : undefined}
          />
          <Box sx={{ width: "75%" }}>
            {typeof errorDetails?.message === "object" && (
              <AlertsAccordion
                severity={errorDetails?.type}
                message={errorDetails?.message as string[]}
                onClose={() => setErrorDetails(undefined)}
              />
            )}
          </Box>
          <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ margin: "15px 0px" }}>
            <Typography sx={ImportModalStyles.body.text.info}>Supported Format: xlsx</Typography>
            <Typography sx={ImportModalStyles.body.text.info}>Maximum Size: 5mb</Typography>
          </Box>
          <Box sx={ImportModalStyles.body.sampleFileRoot}>
            <Box sx={ImportModalStyles.body.sampleFileContainer}>
              <Box display="flex" flexDirection="column" gap={1}>
                <Box display="flex" gap={1}>
                  <Folder />
                  <Typography>{languageConfig.employees.modals.import.sampleFileTitle}</Typography>
                </Box>
                <Typography sx={ImportModalStyles.body.sampleFileSubtitle}>
                  {languageConfig.employees.modals.import.sampleFileSubtitle}
                </Typography>
              </Box>
              <Box sx={ImportModalStyles.downloadIcon} onClick={onDownloadSampleIconClick}>
                <CloudDownload sx={{ fill: "black" }} />
              </Box>
            </Box>
          </Box>
        </Box>
        <Box sx={ImportModalStyles.buttonContainer}>
          <ButtonWithLoading
            onClick={onClose}
            variant="text"
            sx={ImportModalStyles.button}
            disabled={fileImportMutation.isLoading}
          >
            {languageConfig.employees.modals.import.buttons.cancel}
          </ButtonWithLoading>
          <ButtonWithLoading
            onClick={onImportClick}
            disabled={!(files && files?.length > 0)}
            variant="contained"
            sx={ImportModalStyles.button}
            isLoading={fileImportMutation.isLoading}
          >
            {languageConfig.employees.modals.import.buttons.import}
          </ButtonWithLoading>
        </Box>
      </Box>
    </Modal>
  );
};

export default ImportModal;
