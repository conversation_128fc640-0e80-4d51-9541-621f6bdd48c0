import { Grid2 } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import React, { useMemo } from "react";
import { PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";

type Props = {
  form: any;
  isCurrentCompensation?: boolean;
  templates: PayrollTemplateV2[];
  isReadOnlyMode?: boolean;
};
const FixedTemplateFormComponents: React.FC<Props> = ({
  form,
  isCurrentCompensation,
  templates,
  isReadOnlyMode = false,
}) => {
  const templateOptions = useMemo(() => {
    return templates?.map((template) => ({
      label: template.name,
      value: template.name,
    }));
  }, [templates]);
  const compensationDetails = useStore(form.store, (state: any) => state.values);

  return (
    <Grid2 container spacing={2}>
      <Grid2 size={8}>
        <form.AppField
          listeners={{
            onChange: () => {
              const key = isCurrentCompensation ? "current_compensation" : "next_compensation";
              form.setFieldValue(`${key}.components`, []);
            },
          }}
          name={isCurrentCompensation ? "current_compensation.name" : "next_compensation.name"}
        >
          {(field: any) => (
            <field.EffiSelect label="Compensation Template" options={templateOptions} disabled={isReadOnlyMode} />
          )}
        </form.AppField>
      </Grid2>
      <Grid2 size={4}>
        <form.AppField
          name={isCurrentCompensation ? "current_compensation.effective_from" : "next_compensation.effective_from"}
        >
          {(field: any) => (
            <field.EffiDate
              label="Effective Date"
              minDate={
                !isCurrentCompensation && compensationDetails?.current_compensation?.effective_from
                  ? new Date(compensationDetails?.current_compensation?.effective_from)
                  : undefined
              }
              maxDate={isCurrentCompensation ? new Date() : undefined}
            />
          )}
        </form.AppField>
      </Grid2>
    </Grid2>
  );
};

export default FixedTemplateFormComponents;
