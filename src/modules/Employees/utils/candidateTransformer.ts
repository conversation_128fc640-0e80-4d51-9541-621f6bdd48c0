import { EmployeeAdminConfig } from "src/services/api_definitions/payroll.service";
import { store } from "src/store/store.config";
import { CandidateGlobalFormType, stepsKey } from "../EmployeeStepper";
import { INPUT_FIELDS as bankDetailsInputField } from "../config/BankDetails";
import { INPUT_FIELDS as educationDetailsInputField } from "../config/EducationDetails";
import { INPUT_FIELDS as emergencyContactInputField } from "../config/EmergencyDetails";
import { INPUT_FIELDS as employmentDetailsInputField } from "../config/EmploymentDetails";
import { INPUT_FIELDS as employmentHistoryInputField } from "../config/EmploymentHistory";
import { INPUT_FIELDS as familyDetailsInputField } from "../config/FamilyDetails";
import { INPUT_FIELDS as personalInformationInputField } from "../config/PersonalInformation";
import { PhoneNumberType } from "../types/FormDataTypes";

type Dict = {
  [key in string]: unknown;
};

type FormType = {
  [key in string]: Dict[];
};

export type CandidateDetails = {
  first_name: string;
  last_name: string;
  personal_email: string;
  phone: {
    country_code: string;
    number: string;
  };
  next_compensation: EmployeeAdminConfig;
  current_compensation: EmployeeAdminConfig;
  date_of_birth: string;
  aadhaar: string;
  pan: string;
  passport: string;
  uan: string;
  blood_group: string;
  marital_status: string;
  gender: string;
  nationality: string;
  work_experience: Dict[];
  dependents: Dict[];
  emergency_contacts: {
    name: string;
    phone: {
      country_code: string;
      number: string;
    };
    primary: boolean;
    relation: string;
  }[];
  education_details: Dict[];
  bank_account: Dict;
  current_address: Dict;
  permanent_address: Dict;
  date_of_joining: string;
  employment_status: string;
  employee_type: string;
  email: string;
  manager: {
    display_name: string;
    email: string;
  };
  location: string;
  office_address: {
    display_address: string;
  } | null;
  hrbp: string;
  job_title: string;
  department: string;
  business_unit: string;
  work_role: {
    band: {
      name: string;
    };
    grade: {
      name: string;
    };
    level: {
      name: string;
    };
  };
  employee_documents: {
    name: string;
    s3_link: string;
    document_type: string;
  }[];
};

export const transformCandidateDetails = (
  candidateDetails: CandidateDetails | null | undefined,
  isViewOnlyMode = false,
): FormType => {
  if (!candidateDetails) return {};
  const { work_experience, dependents, emergency_contacts, education_details } = candidateDetails;
  const bank_account = candidateDetails.bank_account || {};
  const current_address = candidateDetails.current_address || {};
  const permanent_address = candidateDetails.permanent_address || {};
  const employee_documents = candidateDetails.employee_documents || [];
  const employeeCompenstation = {
    next_compensation: candidateDetails?.next_compensation,
    current_compensation: candidateDetails?.current_compensation,
  };
  if (candidateDetails.email) {
    // this will be used to upload the document
    localStorage.setItem(employmentDetailsInputField.OFFICIAL_EMAIL, candidateDetails.email as string);
  }
  return {
    [stepsKey.personalInformation]: [
      {
        [personalInformationInputField.FIRST_NAME]: candidateDetails.first_name,
        [personalInformationInputField.LAST_NAME]: candidateDetails.last_name,
        [personalInformationInputField.PERSONAL_EMAIL]: candidateDetails.personal_email,
        [personalInformationInputField.MOBILE_NO]: {
          number: candidateDetails.phone?.number || "",
          countryCode: candidateDetails.phone?.country_code || "",
        },
        [personalInformationInputField.DATE_OF_BIRTH]: candidateDetails.date_of_birth,
        [personalInformationInputField.AADHAAR]: candidateDetails.aadhaar,
        [personalInformationInputField.PAN]: candidateDetails.pan,
        [personalInformationInputField.PASSPORT]: candidateDetails.passport,
        [personalInformationInputField.UAN]: candidateDetails.uan,
        [personalInformationInputField.BLOOD_GROUP]: candidateDetails.blood_group,
        [personalInformationInputField.MARITAL_STATUS]: candidateDetails.marital_status,
        [personalInformationInputField.GENDER]: candidateDetails.gender,
        [personalInformationInputField.NATIONALITY]: candidateDetails.nationality,
        [personalInformationInputField.COUNTRY]: current_address.country,
        [personalInformationInputField.STATE]: current_address.state,
        [personalInformationInputField.CITY]: current_address.city,
        [personalInformationInputField.HOUSE]: current_address.address_line1,
        [personalInformationInputField.STREET]: current_address.address_line2,
        [personalInformationInputField.ZIPCODE]: current_address.zip_code,
        [personalInformationInputField.PERMANENT_COUNTRY]: permanent_address.country,
        [personalInformationInputField.PERMANENT_STATE]: permanent_address.state,
        [personalInformationInputField.PERMANENT_CITY]: permanent_address.city,
        [personalInformationInputField.PERMANENT_HOUSE]: permanent_address.address_line1,
        [personalInformationInputField.PERMANENT_STREET]: permanent_address.address_line2,
        [personalInformationInputField.PERMANENT_ZIPCODE]: permanent_address.zip_code,
      },
    ],
    [stepsKey.employementDetails]: [
      {
        [employmentDetailsInputField.EMPLOYEE_TYPE]: candidateDetails.employee_type || "",
        [employmentDetailsInputField.DATE_OF_JOINING]: candidateDetails.date_of_joining,
        [employmentDetailsInputField.OFFICIAL_EMAIL]: candidateDetails.email || "",
        [employmentDetailsInputField.MANAGER]: candidateDetails.manager?.email || "",
        [employmentDetailsInputField.OFFICE_ADDRESS]: candidateDetails.office_address?.display_address || "",
        [employmentDetailsInputField.HRBP]: candidateDetails.hrbp,
        [employmentDetailsInputField.JOB_TITLE]: candidateDetails.job_title || "",
        [employmentDetailsInputField.DEPARTMENT]: candidateDetails.department || "",
        [employmentDetailsInputField.BUSINESS]: candidateDetails.business_unit || "",
        [employmentDetailsInputField.BAND]: candidateDetails.work_role?.band?.name || "",
        [employmentDetailsInputField.GRADE]: candidateDetails.work_role?.grade?.name || "",
        [employmentDetailsInputField.LEVEL]: candidateDetails.work_role?.level?.name || "",
      },
    ],
    [stepsKey.employmentHistory]:
      work_experience?.length > 0
        ? work_experience?.map((item) => ({
            [employmentHistoryInputField.COMPANY]: item.company,
            [employmentHistoryInputField.DESIGNATION]: item.designation,
            [employmentHistoryInputField.FROM_DATE]: item.from_date,
            [employmentHistoryInputField.TO_DATE]: item.to_date,
            [employmentHistoryInputField.FROM_DATE_MAX_DATE]: item.to_date
              ? new Date(item.to_date as string)
              : new Date(),
            [employmentHistoryInputField.TO_DATE_MIN_DATE]: item.from_date
              ? new Date(item.from_date as string)
              : item.from_date,
            [employmentHistoryInputField.TO_DATE_MAX_DATE]: new Date(),
            [employmentHistoryInputField.OFFER_LETTER]: item.offer_letter,
            [employmentHistoryInputField.EXPERIENCE_LETTER]: item.experience_letter,
            id: item?.id,
            [employmentHistoryInputField.IS_CURRENT_EMPLOYER]: !item.to_date,
          }))
        : isViewOnlyMode
          ? []
          : [
              {
                [employmentHistoryInputField.COMPANY]: "",
                [employmentHistoryInputField.DESIGNATION]: "",
                [employmentHistoryInputField.FROM_DATE]: "",
                [employmentHistoryInputField.TO_DATE]: "",
                [employmentHistoryInputField.FROM_DATE_MAX_DATE]: "",
                [employmentHistoryInputField.TO_DATE_MIN_DATE]: "",
                [employmentHistoryInputField.OFFER_LETTER]: null,
                [employmentHistoryInputField.EXPERIENCE_LETTER]: null,
                id: "",
                [employmentHistoryInputField.IS_CURRENT_EMPLOYER]: false,
              },
            ],
    [stepsKey.bankDetails]: [
      {
        [bankDetailsInputField.ACCOUNT_NUMBER]: bank_account.account_number,
        [bankDetailsInputField.IFSC]: bank_account.ifsc,
        [bankDetailsInputField.BANK_NAME]: bank_account.bank_name,
        [bankDetailsInputField.BRANCH]: bank_account.bank_branch,
        [bankDetailsInputField.ACCOUNT_HOLDER_NAME]: bank_account.account_holder_name,
        [bankDetailsInputField.BANK_DOCUMENT]: bank_account.canceled_cheque,
      },
    ],
    [stepsKey.familyDetails]:
      dependents?.length > 0
        ? dependents?.map((item) => ({
            [familyDetailsInputField.FIRST_NAME]: item.first_name,
            [familyDetailsInputField.LAST_NAME]: item.last_name,
            [familyDetailsInputField.DATE_OF_BIRTH]: item.date_of_birth,
            [familyDetailsInputField.RELATION]: item.relation,
            [familyDetailsInputField.DOB_MAX_DATE]: new Date().toDateString(),
          }))
        : isViewOnlyMode
          ? []
          : [
              {
                [familyDetailsInputField.FIRST_NAME]: "",
                [familyDetailsInputField.LAST_NAME]: "",
                [familyDetailsInputField.DATE_OF_BIRTH]: "",
                [familyDetailsInputField.RELATION]: "",
                [familyDetailsInputField.DOB_MAX_DATE]: "",
              },
            ],
    [stepsKey.emergencyDetails]:
      emergency_contacts?.length > 0
        ? emergency_contacts?.map((item) => ({
            [emergencyContactInputField.NAME]: item.name,
            [emergencyContactInputField.PHONE]: {
              countryCode: item.phone?.country_code || "",
              number: item.phone?.number || "",
            },
            [emergencyContactInputField.PRIMARY]: item.primary ? "Yes" : "No",
            [emergencyContactInputField.RELATION]: item.relation,
          }))
        : isViewOnlyMode
          ? []
          : [
              {
                [emergencyContactInputField.NAME]: "",
                [emergencyContactInputField.PHONE]: {
                  countryCode: "",
                  number: "",
                },
                [emergencyContactInputField.PRIMARY]: "",
                [emergencyContactInputField.RELATION]: "",
              },
            ],
    [stepsKey.educationDetails]:
      education_details?.length > 0
        ? education_details?.map((item) => ({
            [educationDetailsInputField.INSTITUTE]: item.institute,
            [educationDetailsInputField.UNIVERSITY]: item.university,
            [educationDetailsInputField.DEGREE]: item.degree,
            [educationDetailsInputField.DEGREE_TYPE]: item.degree_type,
            [educationDetailsInputField.START_YEAR]: item.start_year,
            [educationDetailsInputField.END_YEAR]: item.end_year,
            [educationDetailsInputField.DOCUMENT]: item.employee_document,
            [educationDetailsInputField.START_YEAR_MAX_DATE]: item.end_year,
            [educationDetailsInputField.END_YEAR_MIN_DATE]: item.start_year,
            id: item?.id,
          }))
        : isViewOnlyMode
          ? []
          : [
              {
                [educationDetailsInputField.INSTITUTE]: "",
                [educationDetailsInputField.UNIVERSITY]: "",
                [educationDetailsInputField.DEGREE]: "",
                [educationDetailsInputField.DEGREE_TYPE]: "",
                [educationDetailsInputField.START_YEAR]: "",
                [educationDetailsInputField.END_YEAR]: "",
                [educationDetailsInputField.DOCUMENT]: null,
                [educationDetailsInputField.START_YEAR_MAX_DATE]: "",
                [educationDetailsInputField.END_YEAR_MIN_DATE]: "",
                id: "",
              },
            ],
    [stepsKey.documentUpload]: [
      employee_documents.reduce((acc, item) => {
        acc[item.name] = item;
        return acc;
      }, {} as Dict),
    ],
    [stepsKey.employeeCompensation]: employeeCompenstation,
  };
};

export const transformEmployeePersonalDetails = (candidateDetails: CandidateGlobalFormType) => {
  const personalInformation = candidateDetails[stepsKey.personalInformation][0];
  return {
    first_name: personalInformation[personalInformationInputField.FIRST_NAME],
    last_name: personalInformation[personalInformationInputField.LAST_NAME],
    personal_email: personalInformation[personalInformationInputField.PERSONAL_EMAIL],
    phone:
      (personalInformation[personalInformationInputField.MOBILE_NO] as PhoneNumberType).countryCode +
      (personalInformation[personalInformationInputField.MOBILE_NO] as PhoneNumberType).number,
    date_of_birth: personalInformation[personalInformationInputField.DATE_OF_BIRTH],
    aadhaar: personalInformation[personalInformationInputField.AADHAAR],
    pan: personalInformation[personalInformationInputField.PAN],
    passport: personalInformation[personalInformationInputField.PASSPORT],
    uan: personalInformation[personalInformationInputField.UAN],
    blood_group: personalInformation[personalInformationInputField.BLOOD_GROUP],
    marital_status: personalInformation[personalInformationInputField.MARITAL_STATUS],
    gender: personalInformation[personalInformationInputField.GENDER],
    nationality: personalInformation[personalInformationInputField.NATIONALITY],
    current_address: {
      country: personalInformation[personalInformationInputField.COUNTRY],
      state: personalInformation[personalInformationInputField.STATE],
      city: personalInformation[personalInformationInputField.CITY],
      address_line1: personalInformation[personalInformationInputField.HOUSE],
      address_line2: personalInformation[personalInformationInputField.STREET],
      zip_code: personalInformation[personalInformationInputField.ZIPCODE],
    },
    permanent_address: personalInformation[personalInformationInputField.PERMANENT_HOUSE]
      ? {
          country: personalInformation[personalInformationInputField.PERMANENT_COUNTRY],
          state: personalInformation[personalInformationInputField.PERMANENT_STATE],
          city: personalInformation[personalInformationInputField.PERMANENT_CITY],
          address_line1: personalInformation[personalInformationInputField.PERMANENT_HOUSE],
          address_line2: personalInformation[personalInformationInputField.PERMANENT_STREET],
          zip_code: personalInformation[personalInformationInputField.PERMANENT_ZIPCODE],
        }
      : null,
  };
};

export const transformEmployeeDetailsData = (candidateDetails: CandidateGlobalFormType) => {
  const employementDetails = candidateDetails[stepsKey.employementDetails][0];

  const state = store.getState();
  const { selectedOrganisationDetails } = state.userManagement;
  const officeDisplayAddress = employementDetails[employmentDetailsInputField.OFFICE_ADDRESS];

  const officeAddress = selectedOrganisationDetails?.addresses.find(
    ({ display_address }) => display_address === officeDisplayAddress,
  );

  return {
    employee_type: employementDetails[employmentDetailsInputField.EMPLOYEE_TYPE],
    date_of_joining: employementDetails[employmentDetailsInputField.DATE_OF_JOINING],
    email: employementDetails[employmentDetailsInputField.OFFICIAL_EMAIL],
    manager: {
      email: employementDetails[employmentDetailsInputField.MANAGER],
    },
    office_address: {
      address_line1: officeAddress?.address_line1,
      address_line2: officeAddress?.address_line2,
      city: officeAddress?.city,
      state: officeAddress?.state,
      country: officeAddress?.country,
      zip_code: officeAddress?.zip_code,
    },
    hrbp: employementDetails[employmentDetailsInputField.HRBP],
    job_title: {
      name: employementDetails[employmentDetailsInputField.JOB_TITLE],
      department: {
        name: employementDetails[employmentDetailsInputField.DEPARTMENT],
        business_unit: {
          name: employementDetails[employmentDetailsInputField.BUSINESS],
        },
      },
      work_role: {
        band: employementDetails[employmentDetailsInputField.BAND]
          ? {
              name: employementDetails[employmentDetailsInputField.BAND],
            }
          : undefined,
        grade: employementDetails[employmentDetailsInputField.GRADE]
          ? {
              name: employementDetails[employmentDetailsInputField.GRADE],
            }
          : undefined,
        level: employementDetails[employmentDetailsInputField.LEVEL]
          ? {
              name: employementDetails[employmentDetailsInputField.LEVEL],
            }
          : undefined,
      },
    },
  };
};

export const transformEmployeeHistoryData = (candidateDetails: CandidateGlobalFormType) => {
  const employmentHistory = candidateDetails[stepsKey.employmentHistory];
  return (
    employmentHistory
      ?.map((item: Dict) => ({
        company: item[employmentHistoryInputField.COMPANY],
        designation: item[employmentHistoryInputField.DESIGNATION],
        from_date: item[employmentHistoryInputField.FROM_DATE],
        to_date: item[employmentHistoryInputField.TO_DATE],
        offer_letter: item[employmentHistoryInputField.OFFER_LETTER],
        experience_letter: item[employmentHistoryInputField.EXPERIENCE_LETTER],
        id: item?.["id"] || null,
      }))
      .filter((item) => item.company !== "" && item.company != null) || []
  );
};

export const transformEmployeeBankDetailsData = (candidateDetails: CandidateGlobalFormType) => {
  const bankDetails = candidateDetails[stepsKey.bankDetails][0];
  return bankDetails[bankDetailsInputField.ACCOUNT_NUMBER]
    ? {
        account_number: bankDetails[bankDetailsInputField.ACCOUNT_NUMBER],
        ifsc: bankDetails[bankDetailsInputField.IFSC],
        bank_name: bankDetails[bankDetailsInputField.BANK_NAME],
        bank_branch: bankDetails[bankDetailsInputField.BRANCH],
        account_holder_name: bankDetails[bankDetailsInputField.ACCOUNT_HOLDER_NAME],
        canceled_cheque: bankDetails[bankDetailsInputField.BANK_DOCUMENT],
      }
    : null;
};

export const transformFamilyDetailsData = (candidateDetails: CandidateGlobalFormType) => {
  const familyDetails = candidateDetails[stepsKey.familyDetails];
  return (
    familyDetails
      ?.map((item: Dict) => ({
        first_name: item[familyDetailsInputField.FIRST_NAME],
        last_name: item[familyDetailsInputField.LAST_NAME],
        date_of_birth: item[familyDetailsInputField.DATE_OF_BIRTH],
        relation: item[familyDetailsInputField.RELATION],
      }))
      .filter((item) => item.first_name !== "" && item.first_name != null) || []
  );
};

export const transformEmergencyContactsData = (candidateDetails: CandidateGlobalFormType) => {
  const emergencyContacts = candidateDetails[stepsKey.emergencyDetails];
  return (
    emergencyContacts
      ?.map((item: Dict) => ({
        name: item[emergencyContactInputField.NAME],
        phone:
          (item[emergencyContactInputField.PHONE] as PhoneNumberType).countryCode +
          (item[emergencyContactInputField.PHONE] as PhoneNumberType).number,
        primary: item[emergencyContactInputField.PRIMARY] === "Yes",
        relation: item[emergencyContactInputField.RELATION],
      }))
      .filter((item) => item.name !== "" && item.name != null) || []
  );
};

export const transformEducationDetailsData = (candidateDetails: CandidateGlobalFormType) => {
  const educationDetails = candidateDetails[stepsKey.educationDetails];
  return (
    educationDetails
      ?.map((item: Dict) => ({
        institute: item[educationDetailsInputField.INSTITUTE],
        university: item[educationDetailsInputField.UNIVERSITY],
        degree: item[educationDetailsInputField.DEGREE],
        degree_type: item[educationDetailsInputField.DEGREE_TYPE],
        start_year: item[educationDetailsInputField.START_YEAR],
        end_year: item[educationDetailsInputField.END_YEAR],
        employee_document: item[educationDetailsInputField.DOCUMENT],
        id: item?.["id"],
      }))
      .filter((item) => item.institute !== "" && item.institute != null) || []
  );
};

export const transformDocumentUploadData = (candidateDetails: CandidateGlobalFormType) => {
  const documentUpload = candidateDetails[stepsKey.documentUpload][0];
  return Object.values(documentUpload).filter((d) => d);
};

export const transformEmployeeCompensationData = (employeeDetails: any) => {
  const compensation = employeeDetails[stepsKey.employeeCompensation];

  if (!compensation) return null;
  return {
    current_compensation: compensation?.current_compensation,
    next_compensation: compensation?.next_compensation,
  };
};

export const transformUpdateCandidateDetails = (
  candidateDetails: CandidateGlobalFormType,
  isCreateEmployee = false,
) => {
  const personalDetailsData = transformEmployeePersonalDetails(candidateDetails);
  const employementDetailsData = isCreateEmployee ? transformEmployeeDetailsData(candidateDetails) : {};
  return {
    ...personalDetailsData,
    ...employementDetailsData,
    ...transformEmployeeCompensationData(candidateDetails),
    work_experience: transformEmployeeHistoryData(candidateDetails),
    bank_account: transformEmployeeBankDetailsData(candidateDetails),
    dependents: transformFamilyDetailsData(candidateDetails),
    emergency_contacts: transformEmergencyContactsData(candidateDetails),
    education_details: transformEducationDetailsData(candidateDetails),
    employee_documents: transformDocumentUploadData(candidateDetails),
  };
};
