import validators from "src/utils/validators";
import { FormDataType, FormInputType } from "../types/FormDataTypes";

export const INPUT_FIELDS = {
  FIRST_NAME: "dependents.first_name",
  LAST_NAME: "dependents.last_name",
  DATE_OF_BIRTH: "dependents.date_of_birth",
  RELATION: "dependents.relation",
  DOB_MAX_DATE: "dob_max_date",
};

export const FamilyDetailsInitialValues: FormDataType[] = [
  {
    [INPUT_FIELDS.FIRST_NAME]: "",
    [INPUT_FIELDS.LAST_NAME]: "",
    [INPUT_FIELDS.DATE_OF_BIRTH]: "",
    [INPUT_FIELDS.RELATION]: "",
    [INPUT_FIELDS.DOB_MAX_DATE]: new Date(),
  },
];

export const FamilyDetailsformValidators = {
  [INPUT_FIELDS.FIRST_NAME]: [validators.validateInput, validators.validateName],
  [INPUT_FIELDS.LAST_NAME]: [validators.validateInput, validators.validateName],
  [INPUT_FIELDS.DATE_OF_BIRTH]: [validators.validateInput],
  [INPUT_FIELDS.RELATION]: [validators.validateInput],
};

export const form: FormInputType[] = [
  {
    name: INPUT_FIELDS.FIRST_NAME,
    label: "First Name",
    variant: "text",
    placeholder: "Enter first name",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.LAST_NAME,
    label: "Last Name",
    variant: "text",
    placeholder: "Enter last name",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.DATE_OF_BIRTH,
    label: "Date of Birth",
    variant: "date",
    placeholder: "XX-XX-XXXX",
    isRequired: true,
    maxDate: INPUT_FIELDS.DOB_MAX_DATE,
  },
  {
    name: INPUT_FIELDS.RELATION,
    label: "Relation",
    variant: "select",
    isRequired: true,
  },
];
