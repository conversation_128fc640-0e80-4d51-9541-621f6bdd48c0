import { Add, Arrow<PERSON>ackIos, DeleteOutlined } from "@mui/icons-material";
import { Box, Button, Divider, Typography } from "@mui/material";
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useMemo } from "react";
import { BaseObject } from "src/app/global";
import { FormError, useForm } from "src/customHooks/useForm";
import CustomSelect from "../Common/FormInputs/CustomSelect";
import CustomTextField from "../Common/FormInputs/CustomTextField";
const headingStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
};

const addButtonStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  textTransform: "none",
  margin: 0,
  marginTop: "15px",
};

const backButtonStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  textTransform: "none",
  margin: 0,
  width: "172px",
  height: "48px",
  flexGrow: 0,
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  padding: "0 20px",
  borderRadius: "50px",
  backgroundColor: "#eff4f8",
  border: "#eff4f8",
};

const nextButtonStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  textTransform: "none",
  margin: 0,

  width: "172px",
  height: "48px",
  flexGrow: 0,
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  gap: "8px",
  padding: "0 20px",
  borderRadius: "50px",
  backgroundColor: "#007f6f",
  marginLeft: "auto",
};

export type FormConfig = {
  addButtonText?: string;
  onBackClick?: () => void;
  onNextClick: (formDetails: BaseObject[]) => void;
  onSkipClick?: () => void;
  formTitle: string;
  nextButtonText?: string;
};

interface CommonFormProps {
  selectOptions: BaseObject;
  defaultFormState: BaseObject[];
  formValidators: FormError<Record<string, string>>;
  inputElements: BaseObject[];
  formConfig: FormConfig;
  rowAdditionaInitialValues: BaseObject[];
  captureFormChange?: (ev: React.ChangeEvent<Record<string, never>>, index: number) => void;
}

export const CommonForm = ({
  selectOptions,
  defaultFormState,
  formValidators,
  inputElements,
  formConfig,
  rowAdditionaInitialValues,
  captureFormChange,
}: CommonFormProps) => {
  const {
    formDetails,
    formErrors,
    handleChange,
    addNewFormDetailRow,
    deleteFormDetails,
    areFormDetailsValid,
    handleSelectChange,
  } = useForm({
    initialState: defaultFormState,
    isBulk: true,
    validations: formValidators,
  });

  const areFormDetailsFilled = useMemo(() => {
    return (formDetails as BaseObject[]).every((formDetail: BaseObject) => {
      return inputElements.every((inputElement: any) => {
        // Check if the field is mandatory; if not, consider it as valid (true)
        if (inputElement.allowEmpty) {
          return true;
        }
        // Otherwise, check if the detail is truthy
        return !!formDetail[inputElement.name];
      });
    });
  }, [formDetails, inputElements]);

  const onAddMoreClick = () => {
    addNewFormDetailRow(rowAdditionaInitialValues || []);
  };

  const onDeleteClick = (index: number) => {
    deleteFormDetails(index);
  };

  const onInputChange = (ev: any, index: number) => {
    captureFormChange?.(ev, index);
    handleChange(ev, index);
  };
  const onSelectInputChange = (ev: any, index: number) => {
    captureFormChange?.(ev, index);
    handleSelectChange(ev, ev?.target?.name, index);
  };

  return (
    <Box>
      <Box sx={{ overflowY: "auto", display: "flex", flexDirection: "column", height: "55vh" }}>
        <Typography sx={headingStyle}>{formConfig.formTitle}</Typography>

        {(formDetails as BaseObject[])?.map((formDetail, index: number) => {
          const isReadonlyMode = defaultFormState.length !== 1 && defaultFormState.length > index;

          return (
            <Box sx={{ width: "100%", margin: "30px 0px" }} key={`${index + 1}`}>
              <Box display="flex" flexDirection={"column"}>
                <Box flexWrap={"wrap"} display="flex" flexDirection={"row"} gap={1}>
                  {inputElements.map((inputElement: any) => {
                    const options = inputElement?.isDynamicOptions
                      ? (selectOptions as Record<string, string>)[inputElement.name]?.[index]
                      : selectOptions[inputElement.name];
                    switch (inputElement.type) {
                      case "select":
                        return (
                          <Box sx={{ display: "flex", flexDirection: "column", ...inputElement.style }}>
                            <CustomSelect
                              name={inputElement.name}
                              sx={inputElement.style}
                              onChange={(ev: any) => {
                                onSelectInputChange(ev, index);
                              }}
                              options={options as { value: string; label: string }[]}
                              value={formDetail[inputElement.name] as string}
                              readOnly={isReadonlyMode}
                              // label={inputElement.label}
                              id={inputElement.name}
                              label={inputElement.label}
                              size="small"
                            />
                          </Box>
                        );
                      default:
                        return (
                          <Box sx={{ display: "flex", flexDirection: "column", ...inputElement.style }}>
                            <CustomTextField
                              placeholder={inputElement.placeholder}
                              fullWidth
                              id={inputElement.name}
                              value={formDetail[inputElement.name]}
                              // biome-ignore lint/suspicious/noExplicitAny: <explanation>
                              onChange={(ev: any) => onInputChange(ev, index)}
                              error={!!(formErrors as Record<string, string>)[index][inputElement.name]}
                              helperText={
                                !!(formErrors as Record<string, string>)[index][inputElement.name] &&
                                (formErrors as Record<string, string>)[index][inputElement.name]
                              }
                              inputProps={{ readOnly: isReadonlyMode }}
                              title={inputElement.label}
                              size="small"
                              multiline={inputElement?.rows}
                              rows={inputElement?.rows}
                              // tooltipText={inputElement?.tooltipText}
                            />
                          </Box>
                        );
                    }
                  })}
                </Box>

                {/* biome-ignore lint/suspicious/noExplicitAny: <explanation> */}
                {(formDetails as any)?.length !== 1 && (
                  <React.Fragment>
                    {!isReadonlyMode && (
                      <DeleteOutlined
                        onClick={() => onDeleteClick(index)}
                        sx={{ flex: 1, fill: "#667085", cursor: "pointer", alignSelf: "flex-end", margin: "10px" }}
                      />
                    )}
                    {/* biome-ignore lint/suspicious/noExplicitAny: <explanation> */}
                    {index !== (formDetails as any)?.length - 1 && (
                      <Divider sx={{ width: "100%", margin: "20px 0 -30px" }} />
                    )}
                  </React.Fragment>
                )}
              </Box>
              {/* biome-ignore lint/suspicious/noExplicitAny: <explanation> */}
              {index === (formDetails as any)?.length - 1 && formConfig.addButtonText && (
                <Button
                  startIcon={<Add fontSize="small" />}
                  variant="text"
                  onClick={onAddMoreClick}
                  sx={addButtonStyle}
                >
                  {formConfig.addButtonText}
                </Button>
              )}
            </Box>
          );
        })}
      </Box>

      <Box sx={{ padding: 1, marginTop: 2, display: "flex", justifyContent: "space-between" }}>
        <Box>
          {formConfig.onBackClick && (
            <Button
              startIcon={<ArrowBackIos />}
              sx={backButtonStyle}
              onClick={formConfig.onBackClick}
              variant="outlined"
            >
              Back
            </Button>
          )}
        </Box>
        <Box display={"flex"} gap={2}>
          {formConfig.onSkipClick && (
            <Button sx={backButtonStyle} onClick={formConfig.onSkipClick} variant="outlined">
              Skip
            </Button>
          )}
          <Button
            onClick={() => formConfig.onNextClick(formDetails as BaseObject[])}
            variant="contained"
            disabled={!areFormDetailsValid || !areFormDetailsFilled}
            sx={nextButtonStyle}
          >
            {formConfig?.nextButtonText || "Next"}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};
