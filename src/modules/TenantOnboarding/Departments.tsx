import React from "react";

import { CircularProgress } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";
import { CommonForm } from "./CommonForm";

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const loaderStyle = { width: "100%", marginTop: "25%", marginLeft: "50%" };
const { businessUnits: businessUnitsLang, departments } = languageConfig.tenants.tenantSettings;

export const Departments = ({
  handleNextStep,
  handleBackStep,
}: {
  handleNextStep: () => void;
  handleBackStep: () => void;
}) => {
  const tenantId = getCurrentTenantId();
  const { data: departmentList, isLoading: isDepartmentLoading } = useQuery(
    ["get-all-departments"],
    async () => departmentService.getAllDepartments(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const { data: businessUnits, isLoading: businessUnitsLoading } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const businessUnitsOptions = convertListToOptions(businessUnits as [], "name", "name");
  const deafaultResponse =
    departmentList?.map((businessUnit: BaseObject) => ({
      businessUnits: businessUnit.business_unit,
      department: businessUnit.name,
    })) || [];

  const rowAdditionaInitialValues = [
    {
      businessUnits: "",
      department: "",
    },
  ];
  const defaultFormState: BaseObject[] = deafaultResponse.length ? deafaultResponse : rowAdditionaInitialValues;

  const handleNextClick = async (formDetails: BaseObject[]) => {
    const payload = formDetails.map((formDetail) => ({
      business_unit: formDetail.businessUnits,
      name: formDetail.department,
    }));
    if (payload.length > defaultFormState.length || deafaultResponse.length === 0) {
      await departmentService.setDepartmentDetails(payload);
    }

    handleNextStep();
  };

  const formValidators = {
    businessUnits: [validators.validateInput],
    department: [validators.validateInput],
  };
  const inputElements = [
    {
      name: "businessUnits",
      label: businessUnitsLang.inputTitle,
      type: "select",
      style: { flex: "50%" },
      placeholder: businessUnitsLang.addBusinessUnit,
    },
    {
      name: "department",
      label: departments.inputTitle,
      type: "text",
      style: { flex: "48%" },
      placeholder: departments.enterDepartment,
    },
  ];
  const formConfig = {
    addButtonText: departments.addDepartment,
    onNextClick: handleNextClick,
    formTitle: departments.title,
    onBackClick: handleBackStep,
  };
  const selectOptions = { businessUnits: businessUnitsOptions };

  if (isDepartmentLoading || businessUnitsLoading) return <CircularProgress sx={loaderStyle} />;
  return (
    <CommonForm
      formConfig={formConfig}
      selectOptions={selectOptions}
      defaultFormState={defaultFormState}
      formValidators={formValidators}
      inputElements={inputElements}
      rowAdditionaInitialValues={rowAdditionaInitialValues}
    />
  );
};
