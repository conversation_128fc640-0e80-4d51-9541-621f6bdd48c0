import React from "react";

import { CircularProgress } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";
import { CommonForm } from "./CommonForm";

const { employeeConfig: employeeIDConfigurationLang } = languageConfig.tenants.tenantSettings;

export const EmployeeIDConfiguration = ({
  handleNextStep,
  handleBackStep,
}: {
  handleNextStep: () => void;
  handleBackStep: () => void;
}) => {
  const tenantId = getCurrentTenantId();

  const { data: idConfigDetails, isLoading: idConfigDetailsLoading } = useQuery(
    ["get-employee-id-config-details"],
    async () => departmentService.getEmployeeIDConfig(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const handleNextClick = async (formDetails: BaseObject[]) => {
    const payload = {
      prefix: formDetails[0].prefix,
      sequence: formDetails[0].sequence,
      length: formDetails[0].idLength,
    };
    if (idConfigDetails?.sequence) {
      await departmentService.updateEmployeeIDConfig(payload);
    } else {
      await departmentService.setEmployeeIDConfig({ payload });
    }
    handleNextStep();
  };

  const deafaultResponse: BaseObject[] = [
    {
      prefix: idConfigDetails?.prefix || "",
      sequence: idConfigDetails?.sequence || "",
      idLength: idConfigDetails?.length || "",
    },
  ];

  const rowAdditionaInitialValues = [
    {
      prefix: "",
      sequence: "",
      idLength: "",
    },
  ];
  const defaultFormState: BaseObject[] = idConfigDetails?.sequence ? deafaultResponse : rowAdditionaInitialValues;

  const formValidators = {
    prefix: [],
    sequence: [validators.validateInput],
    idLength: [validators.shouldBeOfMinLength],
  };
  const inputElements = [
    {
      name: "prefix",
      label: employeeIDConfigurationLang.enterPrefix,
      type: "text",
      style: { flex: "30%" },
      placeholder: employeeIDConfigurationLang.prefixTitle,
      allowEmpty: true,
    },
    {
      name: "sequence",
      label: employeeIDConfigurationLang.nextEmployeeNumber,
      type: "text",
      style: { flex: "30%" },
      placeholder: employeeIDConfigurationLang.enterNextEmployeeNumber,
    },
    {
      name: "idLength",
      label: employeeIDConfigurationLang.lengthTitle,
      type: "text",
      style: { flex: "30%" },
      placeholder: employeeIDConfigurationLang.lengthTitle,
      tooltipText: employeeIDConfigurationLang.lengthTooltip,
    },
  ];
  const formConfig = {
    onNextClick: handleNextClick,
    formTitle: employeeIDConfigurationLang.employeeIDConfiguration,
    onBackClick: handleBackStep,
    nextButtonText: employeeIDConfigurationLang.submit,

    // onSkipClick: handleSkipStep,
  };

  if (idConfigDetailsLoading) return <CircularProgress />;
  return (
    <CommonForm
      formConfig={formConfig}
      selectOptions={{}}
      defaultFormState={defaultFormState}
      formValidators={formValidators}
      inputElements={inputElements}
      rowAdditionaInitialValues={rowAdditionaInitialValues}
    />
  );
};
