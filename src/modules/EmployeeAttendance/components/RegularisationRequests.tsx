import { InfoOutlined } from "@mui/icons-material";
import { Box, TableCellProps, Tooltip, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { MRT_ColumnDef } from "material-react-table";
import React from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import { RegularisationRequest } from "src/services/api_definitions/employeeAttendance.service";
import employeeAttendanceService from "src/services/employeeAttendance.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";

const commonProps: Record<string, TableCellProps> = {
  muiTableBodyCellProps: {
    align: "left",
  },
  muiTableHeadCellProps: {
    align: "left",
  },
};

const getStatusCell = (status?: string, comment?: string) => {
  if (!status) {
    return <Typography>--</Typography>;
  }

  return (
    <Box display="flex" alignItems="center" justifyContent="flex-start" gap={1}>
      <Typography fontSize={14} fontWeight={500} color={getStatusColors(status)}>
        {status}
      </Typography>
      {status === "Rejected" && (
        <Tooltip title={comment}>
          <InfoOutlined fontSize="small" sx={{ cursor: "context-menu" }} />
        </Tooltip>
      )}
    </Box>
  );
};

const COLUMN_DEFS: MRT_ColumnDef<RegularisationRequest>[] = [
  {
    accessorKey: "applied_date",
    header: "Applied For",
    size: 200,
    Cell: ({ row }) => formatDateToDayMonthYear(row.original.applied_date),
  },

  {
    accessorKey: "check_in_time",
    header: "Check In",
    size: 100,
    ...commonProps,
  },
  {
    accessorKey: "check_out_time",
    header: "Check Out",
    size: 100,
    ...commonProps,
  },
  {
    accessorKey: "approver",
    header: "Approver",
    size: 200,
  },
  {
    accessorKey: "status",
    header: "Status",
    size: 150,
    ...commonProps,
    Cell: ({
      row: {
        original: { status, comment },
      },
    }) => getStatusCell(status, comment),
  },
];

const RegularisationRequests = () => {
  const {
    data: regularisationRequests,
    isLoading,
    isFetching,
  } = useQuery(["regularisation-requests"], employeeAttendanceService.getAttendanceRegularisationRequests, {
    refetchOnWindowFocus: false,
  });

  return (
    <Box display="flex" flexDirection="column" gap={2} width="100%">
      <DataTable
        enableMultiRowSelection
        data={regularisationRequests || []}
        state={{
          showSkeletons: isLoading || isFetching,
        }}
        columns={COLUMN_DEFS}
      />
    </Box>
  );
};

export default RegularisationRequests;
