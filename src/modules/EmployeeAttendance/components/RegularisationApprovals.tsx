import { InfoOutlined } from "@mui/icons-material";
import { Box, Button, TableCellProps, Tooltip, Typography } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_ColumnDef, MRT_RowSelectionState } from "material-react-table";
import React, { useCallback, useMemo, useState } from "react";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import { RegularisationRequest } from "src/services/api_definitions/employeeAttendance.service";
import employeeAttendanceService from "src/services/employeeAttendance.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";
import ActivityLogDetailsView from "./components/ActivityLogDetailsView";
import RejectionConfirmationModal from "./components/RejectionConfirmationModal";

const typography = {
  approve: "Approve",
  reject: "Reject",
};

enum RegularisationApprovalStatus {
  PENDING = "Pending",
  REJECTED = "Rejected",
}

const commonProps: Record<string, TableCellProps> = {
  muiTableBodyCellProps: {
    align: "left",
  },
  muiTableHeadCellProps: {
    align: "left",
  },
};

const RegularisationApprovals = () => {
  const {
    data: regularisationRequests,
    isLoading,
    isFetching,
    refetch,
  } = useQuery(["regularisation-requests"], employeeAttendanceService.getAttendanceRegularisationApprovals, {
    refetchOnWindowFocus: false,
  });
  const [isAttendanceDetailsModalOpen, setIsAttendanceDetailsModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState<RegularisationRequest | null>(null);
  const [modalStates, setModalState] = useState({
    approve: false,
    reject: false,
  });
  const [rowSelection, setRowSelection] = useState<MRT_RowSelectionState>({});
  const areActionsEnabled = Object.keys(rowSelection).length > 0;

  const selectedRows = useMemo(() => {
    return Object.keys(rowSelection)
      .map(Number)
      .map((index) => regularisationRequests?.[index])
      .filter((row) => row && row?.status === RegularisationApprovalStatus.PENDING);
  }, [regularisationRequests, rowSelection]);

  const selectedRequests = useMemo(() => {
    return selectedRows
      .filter((row): row is RegularisationRequest => !!row?.request_id && !!row?.applied_date)
      .map((row) => ({
        request_id: row.request_id,
        applied_date: row.applied_date,
      }));
  }, [selectedRows]);

  const onAttendanceLogClick = (row: RegularisationRequest) => {
    setIsAttendanceDetailsModalOpen(true);
    setSelectedRow(row);
  };

  const bulkApproveRegularisation = useMutation({
    mutationKey: ["bulk-approve-regularisation"],
    mutationFn: async () => employeeAttendanceService.bulkApproveRegularisationRequest(selectedRequests),
    onSuccess: () => {
      refetch();
      setRowSelection({});
      setModalState({
        approve: false,
        reject: false,
      });
    },
  });

  const bulkRejectRegularisation = useMutation({
    mutationKey: ["bulk-reject-regularisation"],
    mutationFn: async (comment: string) =>
      employeeAttendanceService.bulkRejectRegularisationRequest(selectedRequests, comment),
    onSuccess: () => {
      refetch();
      setRowSelection({});
    },
  });

  const handleBulkApprove = () => {
    bulkApproveRegularisation.mutate();
  };

  const handleBulkRejectionOfRequests = (comment: string) => {
    bulkRejectRegularisation.mutate(comment);
    setModalState({
      approve: false,
      reject: false,
    });
  };

  const onModalClose = () => {
    setModalState({
      approve: false,
      reject: false,
    });
    setRowSelection({});
  };

  const getColumnDefs = useCallback(
    (): MRT_ColumnDef<RegularisationRequest>[] => [
      {
        accessorKey: "raised_by.display_name",
        header: "Employee",
        size: 200,
        Cell: ({
          row: {
            original: {
              raised_by: { display_name, job_title, display_pic },
            },
          },
        }) => <EmployeeCellInfo name={display_name} jobTitle={job_title} displayPic={display_pic} />,
      },
      {
        accessorKey: "applied_date",
        header: "Applied For",
        size: 200,
        Cell: ({ row }) => formatDateToDayMonthYear(row.original.applied_date),
      },
      {
        accessorKey: "check_in_time",
        header: "Check In",
        size: 150,
        ...commonProps,
      },
      {
        accessorKey: "check_out_time",
        header: "Check Out",
        size: 150,
        ...commonProps,
      },
      {
        accessorKey: "duration",
        header: "Duration",
        size: 150,
        ...commonProps,
      },
      {
        header: "Attendance Log",
        ...commonProps,
        Cell: ({ row }) => {
          return (
            <Button
              role="button"
              disabled={row?.original?.details?.length === 0}
              onClick={() => onAttendanceLogClick(row.original)}
            >
              View
            </Button>
          );
        },
      },
      {
        accessorKey: "reason",
        header: "Reason",
      },
      {
        accessorKey: "status",
        Cell: ({ row }) => (
          <Box display="flex" alignItems="center" gap={1}>
            <Typography color={getStatusColors(row?.original?.status)}>{row?.original?.status}</Typography>
            {row?.original?.comment && (
              <Tooltip title={row?.original?.comment}>
                <InfoOutlined color="action" />
              </Tooltip>
            )}
          </Box>
        ),
        header: "Status",
      },
    ],
    [],
  );

  return (
    <Box display="flex" flexDirection="column" gap={2} width="100%">
      <Box display="flex" justifyContent="flex-end" gap={1}>
        <Button
          disabled={!areActionsEnabled}
          variant="contained"
          color="primary"
          onClick={() => setModalState({ approve: true, reject: false })}
        >
          {typography.approve}
        </Button>
        <Button
          disabled={!areActionsEnabled}
          variant="outlined"
          color="error"
          onClick={() => setModalState({ approve: false, reject: true })}
        >
          {typography.reject}
        </Button>
      </Box>
      <DataTable
        layoutMode="grid"
        enableMultiRowSelection
        enableRowSelection={(row) => row?.original?.status === RegularisationApprovalStatus.PENDING}
        onRowSelectionChange={setRowSelection}
        data={regularisationRequests || []}
        state={{
          showSkeletons: isLoading || isFetching,
          rowSelection: rowSelection,
        }}
        columns={getColumnDefs()}
      />
      {modalStates.reject && selectedRequests.length > 0 && (
        <RejectionConfirmationModal
          onClose={onModalClose}
          onSubmit={handleBulkRejectionOfRequests}
          title="Reject Regularisation Request(s)"
        />
      )}
      {isAttendanceDetailsModalOpen && (
        <ActivityLogDetailsView
          isModalOpen={isAttendanceDetailsModalOpen}
          attendanceLogs={selectedRow?.details || []}
          onClose={() => {
            setIsAttendanceDetailsModalOpen(false);
            setSelectedRow(null);
          }}
          selectedDate={selectedRow?.applied_date || ""}
        />
      )}
      {modalStates.approve && (
        <Modal
          isOpen={modalStates.approve}
          title="Approve Regularisation Request(s)"
          onClose={() => setModalState({ ...modalStates, approve: false })}
          sx={{ borderRadius: "20px" }}
        >
          <Box padding={2}>
            <Typography marginBottom="20px">
              You are about to approve {selectedRequests.length} regularisation request
              {selectedRequests.length > 1 ? "s" : ""}. Do you want to proceed?
            </Typography>
            <Box display="flex" justifyContent="flex-end">
              <Button variant="contained" onClick={handleBulkApprove}>
                Approve
              </Button>
            </Box>
          </Box>
        </Modal>
      )}
    </Box>
  );
};

export default RegularisationApprovals;
