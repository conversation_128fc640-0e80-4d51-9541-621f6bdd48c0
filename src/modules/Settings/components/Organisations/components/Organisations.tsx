import { Delete, Edit } from "@mui/icons-material";
import { Box, Button, IconButton, Tooltip } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_Row } from "material-react-table";
import React, { useCallback, useState } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import DataTable from "src/modules/Common/Table/DataTable";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { Address, OrganisationDetails } from "src/services/api_definitions/organisations.service";
import organisationsService from "src/services/organisations.service";
import { getACLFromFeaturekey } from "src/utils/screenUtils";
import AddEditOrganisationModal from "./AddEditOrganisationModal";
import AddressesModal from "./AddressesModal";
import DeleteConfirmationModal from "./DeleteConfirmationModal";

const Organisations = () => {
  const { selectedRole } = useAppSelector((state) => state.userManagement);
  const ORGANISATIONS_ACL = getACLFromFeaturekey(PATH_CONFIG.ORGANISATIONS.key);
  const { data, isFetched, refetch } = useQuery(
    ["get-organisations", selectedRole],
    async () => await organisationsService.getOrganisations(),
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [addresses, setAddresses] = useState<Address[] | null>(null);
  const [deleteKey, setDeleteKey] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<OrganisationDetails | null>(null);

  const deleteMutation = useMutation({
    mutationKey: ["delete-organisation"],
    mutationFn: async () => organisationsService.deleteOrganisation(deleteKey as string),
    onSuccess: () => {
      refetch();
      setDeleteKey(null);
    },
  });

  const onEditClick = (details: OrganisationDetails) => {
    setSelectedRow(details);
    setIsModalOpen(true);
  };

  const onDeleteClick = (deleteKey: string) => {
    setDeleteKey(deleteKey);
  };

  const getEditRow = useCallback(
    (row: MRT_Row<OrganisationDetails>) => (
      <Box width={200}>
        <IconButton disabled={!ORGANISATIONS_ACL?.canWrite} onClick={() => onEditClick(row.original)}>
          <Tooltip title="Edit">
            <Edit />
          </Tooltip>
        </IconButton>
        <IconButton disabled={!ORGANISATIONS_ACL?.canWrite} onClick={() => onDeleteClick(row?.original.name)}>
          <Tooltip title="Delete">
            <Delete />
          </Tooltip>
        </IconButton>
      </Box>
    ),
    [ORGANISATIONS_ACL?.canWrite],
  );

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader title="Organisations" buttonTitle="Add Organisation" primaryAction={() => setIsModalOpen(true)} />
      <DataTable
        key={`${isFetched}`}
        data={data as OrganisationDetails[]}
        initialState={{
          showSkeletons: !isFetched,
        }}
        enableRowActions
        positionActionsColumn="last"
        renderRowActions={({ row }) => getEditRow(row)}
        columns={[
          {
            accessorKey: "logo",
            header: "Company logo",
            Cell: ({ row }: { row: MRT_Row<OrganisationDetails> }) => (
              <img
                src={row?.original?.logo as string}
                style={{ width: "auto", height: "50px", maxWidth: "150px", objectFit: "contain" }}
                alt="logo"
              />
            ),
          },
          {
            accessorKey: "name",
            header: "Name",
          },
          {
            accessorKey: "hr_admin_email",
            header: "HR Admin Email",
          },
          {
            accessorKey: "hr_admin_name",
            header: "HR Admin Name",
          },
          {
            accessorKey: "status",
            header: "Status",
          },
          {
            accessorKey: "addresses",
            header: "Addresses",
            Cell: ({ row }) => (
              <Button variant="text" onClick={() => setAddresses(row?.original?.addresses)}>
                View
              </Button>
            ),
          },
        ]}
      />
      {isModalOpen && (
        <AddEditOrganisationModal
          isModalOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedRow(null);
          }}
          refetch={refetch}
          selectedRow={selectedRow}
        />
      )}
      {addresses && addresses?.length > 0 && (
        <AddressesModal addresses={addresses} onClose={() => setAddresses(null)} isModalOpen={addresses.length > 0} />
      )}
      {deleteKey && (
        <DeleteConfirmationModal
          onCancel={() => setDeleteKey(null)}
          onDelete={() => deleteMutation.mutate()}
          selectedValue={deleteKey}
          isModalOpen={!!deleteKey}
          key={deleteKey}
        />
      )}
    </Box>
  );
};

export default Organisations;
