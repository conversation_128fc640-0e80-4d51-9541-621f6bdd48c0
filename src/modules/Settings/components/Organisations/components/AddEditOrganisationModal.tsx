import { Delete } from "@mui/icons-material";
import { Box, Button, DialogActions, Grid2, IconButton, Paper, Tooltip, Typography } from "@mui/material";
import { nanoid } from "@reduxjs/toolkit";
import { useMutation } from "@tanstack/react-query";
import { APIProvider, MapMouseEvent } from "@vis.gl/react-google-maps";
import React, { useMemo, useState } from "react";
import { OrganisationIcon } from "src/assets/icons.svg";
import { globalEnvConfig } from "src/configs/global.config";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import AddMore from "src/modules/Common/Buttons/AddMore";
import FileDropzone, { FileDropVariants } from "src/modules/Common/FileDropzone/FileDropzone";
import Modal from "src/modules/Common/Modal/Modal";
import { CommonForm } from "src/modules/Employees/components/CommonForm";
import { FormInputType } from "src/modules/Employees/types/FormDataTypes";
import { apiRegister } from "src/services";
import { Address, Geofence, OrganisationDetails } from "src/services/api_definitions/organisations.service";
import fileuploaderService from "src/services/fileuploader.service";
import locationService, { ZIPCODE_APIResponse } from "src/services/location.service";
import organisationsService from "src/services/organisations.service";
import { deepEqualArrays } from "src/utils/dataUtils";
import validators from "src/utils/validators";
import GoogleMaps from "./GoogleMaps";

interface LeaveModalProps {
  readonly onClose: () => void;
  readonly isModalOpen: boolean;
  readonly refetch: () => void;
  readonly selectedRow: OrganisationDetails | null;
}

type OrganisationBasicDetailDefaultState = {
  name: string;
  hrAdminEmail: string;
  hrAdminName: string;
  logo: string;
};

const defaultFormState = {
  name: "",
  hrAdminEmail: "",
  hrAdminName: "",
  logo: "",
};

const addressFormState = {
  address_line1: "",
  address_line2: "",
  city: "",
  country: "",
  state: "",
  zip_code: "",
  geofence_enabled: false,
  latitude: "",
  longitude: "",
  geofence_radius_meters: "",
};

const addressBulkForm: Address[] = [addressFormState];

const addressFormStruct: FormInputType[] = [
  {
    name: "address_line1",
    isRequired: true,
    label: "Address Line 1",
    variant: "text",
  },
  {
    name: "address_line2",
    isRequired: false,
    label: "Address Line 2",
    variant: "text",
  },
  {
    name: "zip_code",
    isRequired: true,
    label: "Zip Code",
    variant: "text",
  },
  {
    name: "city",
    isRequired: true,
    label: "City",
    variant: "text",
  },
  {
    name: "state",
    isRequired: true,
    label: "State",
    variant: "text",
  },
  {
    name: "country",
    isRequired: true,
    label: "Country",
    variant: "text",
  },
  {
    name: "geofence_enabled",
    isRequired: false,
    label: "Geofencing",
    variant: "checkbox",
  },
];

const baseFormStruct: FormInputType[] = [
  {
    name: "name",
    variant: "text",
    label: "Name",
    isRequired: true,
  },
  {
    name: "hrAdminEmail",
    variant: "text",
    label: "HR Admin Email",
    isRequired: true,
  },
  {
    name: "hrAdminName",
    variant: "text",
    label: "HR Admin Name",
    isRequired: true,
  },
];

const geoFencingFormStruct: FormInputType[] = [
  {
    name: "latitude",
    isRequired: true,
    label: "Latitude",
    variant: "number",
  },
  {
    name: "longitude",
    isRequired: true,
    label: "Longitude",
    variant: "number",
  },
  {
    name: "geofence_radius_meters",
    isRequired: true,
    label: "Geofencing Radius (In meters)",
    variant: "number",
    placeholder: "In meters",
    props: {
      slotProps: {
        htmlInput: {
          min: 0,
        },
      },
    },
  },
];

function isAddressFormComplete(address: Address) {
  const keysToIgnore = ["address_line2", "geofence_enabled", "geofence_radius_meters", "latitude", "longitude"];
  return Object.keys(address)
    .filter((key) => !keysToIgnore.includes(key))
    .every((eachAddressKey) => {
      return address[eachAddressKey as keyof typeof address];
    });
}

function checkAddressForms(address: Address[]) {
  return address.every(isAddressFormComplete);
}

function checkGeofencingForms(address: Address[]) {
  return address
    .filter((eachAddress) => eachAddress.geofence_enabled)
    .every((form) => {
      return form.latitude && form.longitude && form.geofence_radius_meters && Number(form.geofence_radius_meters) > 0;
    });
}

const AddEditOrganisationModal: React.FC<LeaveModalProps> = ({ isModalOpen, onClose, refetch, selectedRow }) => {
  const [files, setFiles] = useState<File[] | null>(null);
  const [fileUploadResponseVariant, setFileUploadResponseVariant] = useState("default");

  const formInititalState = useMemo(() => {
    return selectedRow
      ? {
          ...defaultFormState,
          name: selectedRow?.name || "",
          hrAdminEmail: selectedRow?.hr_admin_email || "",
          hrAdminName: selectedRow?.hr_admin_name || "",
          logo: selectedRow?.logo || "",
        }
      : defaultFormState;
  }, [selectedRow, defaultFormState]);
  const {
    formDetails,
    formErrors,
    areFormDetailsValid,
    setFormDetail: setBasicFormDetail,
  } = useForm({
    initialState: formInititalState,
    isBulk: false,
    validations: {
      name: [validators.validateInput],
      hrAdminEmail: [validators.validateInput, validators.validateEmail],
      hrAdminName: [validators.validateInput, validators.shouldNotContainSpecialCharacters],
      logo: [],
    },
  });

  const initialAddressFormState = useMemo(() => {
    return selectedRow?.addresses?.length ? selectedRow?.addresses : addressBulkForm;
  }, [selectedRow, addressBulkForm]);

  const {
    formDetails: addressFormDetails,
    formErrors: addressFormErrors,
    setFormDetail,
    addNewFormDetailRow,
    deleteFormDetails,
  } = useForm({
    initialState: initialAddressFormState,
    isBulk: true,
    validations: {
      address_line1: [validators.validateInput],
      address_line2: [],
      city: [validators.validateInput],
      country: [validators.validateInput],
      state: [validators.validateInput],
      zip_code: [validators.validateInput, validators.shouldBeNumeric],
      geofence_enabled: [],
      latitude: [],
      longitude: [],
      geofence_radius_meters: [validators.shouldBePositive],
    },
  });

  const typedFormDetails = formDetails as OrganisationBasicDetailDefaultState;
  const typedFormErrors = formErrors as Record<keyof OrganisationBasicDetailDefaultState, string>;
  const id = nanoid();

  const uploadLogoAndGetPath = async (selectedFiles: File[]) => {
    const [_, extenstion] = selectedFiles[0].name.split(".");
    const hostname = window.location.hostname.split(".")[0];
    const fileName = `${hostname}-logo-${id}-org.${extenstion}`;
    const formData = new FormData();
    formData.append("file", selectedFiles[0]);
    formData.append("key", fileName);

    const fileUploadResponse = await fileuploaderService.uploadFile(apiRegister.AWS_S3.paths["upload-logo"], formData);
    setFileUploadResponseVariant(fileUploadResponse?.type);
    if (fileUploadResponse.type === "success") {
      return fileUploadResponse.message;
    }

    return null;
  };

  const getDetailsfromZipcode = useMutation({
    mutationKey: ["get-address-info"],
    mutationFn: (zipcode: string): Promise<ZIPCODE_APIResponse | null> =>
      locationService.getAddressDetailsFromZipcode(zipcode),
  });

  const enrichAddressWithLatitudeLongitude = (addresses: Address[]) => {
    return addresses.map((address) => {
      const isGeofenceEnabled = address?.geofence_enabled;
      return {
        ...address,
        latitude: !isGeofenceEnabled ? null : Number(address?.latitude),
        longitude: !isGeofenceEnabled ? null : Number(address?.longitude),
        geofence_radius_meters: !isGeofenceEnabled ? null : Number(address?.geofence_radius_meters),
      };
    });
  };

  const createOrganisationMutation = useMutation({
    mutationKey: ["create-organisation"],
    mutationFn: async () => {
      return organisationsService.createOrganisation({
        hr_admin_email: typedFormDetails.hrAdminEmail,
        hr_admin_name: typedFormDetails.hrAdminName,
        logo: typedFormDetails?.logo,
        name: typedFormDetails.name,
        addresses: enrichAddressWithLatitudeLongitude(addressFormDetails as Address[]),
      });
    },
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const updateOrganisationMutation = useMutation({
    mutationKey: ["update-leave-type"],
    mutationFn: async () =>
      organisationsService.updateOrganisation({
        hr_admin_email: selectedRow?.hr_admin_email as string,
        hr_admin_name: selectedRow?.hr_admin_name as string,
        logo: selectedRow?.logo as string,
        name: selectedRow?.name as string,
        new_name: typedFormDetails.name,
        new_logo: typedFormDetails?.logo,
        new_hr_admin_email: typedFormDetails.hrAdminEmail as string,
        new_hr_admin_name: typedFormDetails?.hrAdminName as string,
        new_addresses: enrichAddressWithLatitudeLongitude(addressFormDetails as Address[]),
      }),
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const areSelectedRowsEqualToDetailsForm = useMemo(() => {
    const nonGeofenceSelectedRowAddressess = selectedRow?.addresses?.filter((address) => !address.geofence_enabled);
    const nonGeofenceAddressFormDetails = (addressFormDetails as Address[])?.filter(
      (address) => !address.geofence_enabled,
    );

    const areNonGeofenceAddressessEqual = deepEqualArrays(
      nonGeofenceSelectedRowAddressess as any,
      nonGeofenceAddressFormDetails,
    );

    const geofenceSelectedRowAddressess = selectedRow?.addresses
      ?.filter((address) => address.geofence_enabled)
      .map((address) => ({
        latitude: Number(address?.latitude),
        longitude: Number(address?.longitude),
        geofence_radius_meters: Number(address?.geofence_radius_meters),
      }));

    const geofenceAddressFormDetails = (addressFormDetails as Address[])
      ?.filter((address) => address.geofence_enabled)
      .map((address) => ({
        latitude: Number(address?.latitude),
        longitude: Number(address?.longitude),
        geofence_radius_meters: Number(address?.geofence_radius_meters),
      }));
    const arGeofenceAddressessEqual = deepEqualArrays(
      geofenceSelectedRowAddressess as any,
      geofenceAddressFormDetails as Geofence[],
    );

    const areAddressessEqual = deepEqualArrays(selectedRow?.addresses as Address[], addressFormDetails as Address[]);

    const areEqual =
      selectedRow?.name === typedFormDetails?.name &&
      selectedRow?.hr_admin_email === typedFormDetails?.hrAdminEmail &&
      selectedRow?.hr_admin_name === typedFormDetails?.hrAdminName &&
      selectedRow?.logo === typedFormDetails?.logo &&
      areAddressessEqual &&
      arGeofenceAddressessEqual &&
      areNonGeofenceAddressessEqual;
    return areEqual;
  }, [selectedRow, addressFormDetails, formDetails, typedFormDetails]);

  const hasErrors = useMemo(() => {
    const areAnyFormDetailsEmpty = Object.keys(typedFormDetails).some(
      (detailKey) => !typedFormDetails[detailKey as keyof typeof typedFormDetails],
    );
    const areAddressFieldsPresent = checkAddressForms(addressFormDetails as Address[]);
    const areGeofencingFieldsPresent = checkGeofencingForms(addressFormDetails as Address[]);
    const isValid =
      areAnyFormDetailsEmpty || !areFormDetailsValid || !areAddressFieldsPresent || !areGeofencingFieldsPresent;

    if (selectedRow) {
      return isValid || areSelectedRowsEqualToDetailsForm;
    }
    return isValid;
  }, [typedFormDetails, areFormDetailsValid, addressFormDetails]);

  const onSaveClick = async () => {
    if (selectedRow) {
      updateOrganisationMutation.mutate();
      return;
    }
    createOrganisationMutation.mutate();
  };

  const onChange = async (index: number, fieldName: string, value: unknown) => {
    setFormDetail(fieldName, value, index);
    if (fieldName === "geofence_radius_meters") {
      setFormDetail(fieldName, Number(value), index);
    }
    if (fieldName === "zip_code" && (value as string).length >= 6) {
      const resp = await getDetailsfromZipcode.mutateAsync(value as string);
      if (resp) {
        Object.keys(resp).forEach((zipCodeDetailKey) =>
          setFormDetail(zipCodeDetailKey, resp[zipCodeDetailKey as keyof typeof resp], index),
        );
      }
    }
  };

  const onFileDrop = async (data: File[]) => {
    setFiles(data);
    const path = await uploadLogoAndGetPath(data);

    if (path) {
      setBasicFormDetail("logo", path);
    }
  };

  const onAddMoreClick = () => {
    addNewFormDetailRow([addressFormState]);
  };
  // disabledInputFields={(addressFormDetails as unknown as Address[]).map((_row) => getDisabledFields(_row))}
  const onMapChange = (ev: MapMouseEvent["detail"]["latLng"], index: number) => {
    // this id is required to identify the row in the form.
    setFormDetail("latitude", ev?.lat, index);
    setFormDetail("longitude", ev?.lng, index);
  };

  return (
    <Modal
      title={selectedRow ? "Edit Organisation" : "Add Organisation"}
      subtitle={selectedRow ? "Edit organisation accordingly" : "Add organisation accordingly with images & logos"}
      showBackButton
      isOpen={isModalOpen}
      onClose={onClose}
      fullWidth
      actions={
        <DialogActions sx={{ margin: 2 }}>
          <Button disabled={hasErrors} size="large" variant="contained" onClick={onSaveClick}>
            {languageConfig.tenants.button.save}
          </Button>
        </DialogActions>
      }
    >
      <Grid2 container spacing={3}>
        <Grid2 size={12}>
          <Box display="flex" alignItems="flex-start" justifyContent="space-between" gap={2} sx={{ minHeight: 150 }}>
            {selectedRow?.logo ? (
              <img
                width={80}
                key={selectedRow?.name}
                src={selectedRow?.logo}
                alt={selectedRow?.name}
                style={{
                  maxWidth: "100%",
                  maxHeight: 150,
                  objectFit: "contain",
                  flex: "0 0 20%",
                  paddingTop: "16px",
                }}
              />
            ) : (
              <OrganisationIcon sx={{ flex: "0 0 20%", alignSelf: "flex-start", maxHeight: 150 }} />
            )}
            <Box flex="0 0 30%">
              <Typography fontSize={16}>Organisation Logo</Typography>
              <Typography fontSize={10}>
                Update your company logo and then choose where you want it to display
              </Typography>
            </Box>

            {/* Dropzone - 50% width */}
            <Box flex="0 0 45%">
              <FileDropzone
                files={files}
                acceptFileTypes={{
                  "image/jpeg": [".jpg", ".jpeg"],
                  "image/png": [".png"],
                  "image/svg+xml": [".svg"],
                }}
                height={150}
                onFileDrop={onFileDrop}
                variant={fileUploadResponseVariant as FileDropVariants}
              />
            </Box>
          </Box>
        </Grid2>
        <Grid2 size={12}>
          <CommonForm
            formErrors={typedFormErrors}
            formValues={typedFormDetails}
            onChange={setBasicFormDetail}
            inputElements={baseFormStruct}
          />
        </Grid2>
        <Grid2 container size={12} spacing={2}>
          <Typography lineHeight={2}>Address Details</Typography>
          {(addressFormDetails as Address[])?.map((eachFormDetail, index) => (
            <Grid2 container key={index} component={Paper} elevation={2} padding={2} spacing={2} gap={2}>
              <Grid2 size={12}>
                <CommonForm
                  key={index}
                  formErrors={(addressFormErrors as Record<keyof Address, string>[])[index]}
                  formValues={eachFormDetail as any}
                  onChange={(name: string, value: unknown) => onChange(index, name, value)}
                  inputElements={addressFormStruct}
                  disabledInputFields={{
                    country: true,
                    state: true,
                  }}
                />
              </Grid2>
              {eachFormDetail.geofence_enabled && (
                <Grid2 size={12}>
                  <Box display="flex" flexDirection="column" gap={2}>
                    <APIProvider
                      apiKey={globalEnvConfig.GOOGLE_MAPS_API_KEY}
                      onLoad={() => console.log("Maps API has loaded.")}
                    >
                      <GoogleMaps
                        defaultCoords={{
                          lat: (eachFormDetail.latitude as number) || 0,
                          lng: (eachFormDetail.longitude as number) || 0,
                        }}
                        onChange={(ev) => onMapChange(ev, index)}
                        radius={eachFormDetail?.geofence_radius_meters as number}
                      />
                    </APIProvider>
                    <CommonForm
                      formErrors={(addressFormErrors as Record<keyof Address, string>[])[index]}
                      formValues={eachFormDetail as any}
                      disabledInputFields={{
                        latitude: true,
                        longitude: true,
                      }}
                      inputElements={geoFencingFormStruct}
                      onChange={(name: string, value: unknown) => onChange(index, name, value)}
                    />
                  </Box>
                </Grid2>
              )}
              <Grid2 size={12} textAlign="right">
                {(addressFormDetails as Address[]).length > 1 && (
                  <Tooltip title="Delete">
                    <IconButton
                      onClick={() => deleteFormDetails(index)}
                      aria-roledescription="delete"
                      aria-description="delete"
                      aria-label="delete address"
                      aria-haspopup="false"
                      aria-controls={`delete-address-${index}`}
                      color="error"
                    >
                      <Delete />
                    </IconButton>
                  </Tooltip>
                )}
              </Grid2>
            </Grid2>
          ))}
        </Grid2>
        <Grid2 size={2}>
          <AddMore onClick={onAddMoreClick} />
        </Grid2>
      </Grid2>
    </Modal>
  );
};

export default AddEditOrganisationModal;
