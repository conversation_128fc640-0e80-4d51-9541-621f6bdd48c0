import { useQuery } from "@tanstack/react-query";
import React, { useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import businessunitsService from "src/services/businessunits.service";
import tenantsService from "src/services/tenants.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";

import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import { CrudTable } from "src/modules/Settings/components/Common/CrudTable";

const { businessUnits: businessUnitsLang, descriptionLable } = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const getRequestPayload = (formDetail: BaseObject) => {
  return {
    name: formDetail.name,
    cost_center: formDetail.costCenter,
    description: formDetail.description,
  };
};

export const BusinessUnit = () => {
  const tenantId = getCurrentTenantId();
  const { data: costCenters, isLoading } = useQuery(
    ["cost-center-details"],
    async () => tenantsService.getCostCenterDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const {
    data: businessUnits,
    isLoading: businessUnitsLoading,
    refetch: refetchBU,
  } = useQuery(["get-business-unit-details"], async () => businessunitsService.getBusinessUnitDetails(), {
    enabled: !!tenantId,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  const centerCodesOptions = convertListToOptions(costCenters as [], "code", "code");

  const deafaultResponse =
    businessUnits?.map((businessUnit) => ({
      name: businessUnit.name,
      costCenter: businessUnit.cost_center,
      description: businessUnit.description,
    })) || [];

  const rowAdditionaInitialValues = {
    name: "",
    costCenter: "",
    description: "",
  };
  const defaultFormState: BaseObject[] = deafaultResponse;

  const postFormSubmit = () => {
    refetchBU();
  };

  const handleEditDetailsClick = async (formDetails: BaseObject, selectedIndex: number) => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = {
      new_name: parsedData.name,
      name: businessUnits?.[selectedIndex]?.name,
      cost_center: parsedData?.cost_center,
      description: parsedData?.description,
    };
    await businessunitsService.updateBusinessUnitDetails(requestObject);
    postFormSubmit();
  };

  const handleAddDetailsClick = async (formDetails: BaseObject) => {
    const payload = getRequestPayload(formDetails);
    await businessunitsService.setBusinessUnitDetails([payload]);
    postFormSubmit();
  };

  const handleDeleteConfirmed = async (index: number) => {
    await businessunitsService.deleteBusinessUnitDetails(businessUnits?.[index] || {});
    postFormSubmit();
  };

  const formValidators = {
    name: [validators.validateInput],
    costCenter: [validators.validateInput],
    description: [],
  };

  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const selectedRowData = useMemo(
    () => (selectedRow !== null ? deafaultResponse?.[selectedRow] : rowAdditionaInitialValues),
    [selectedRow],
  );

  const useFormDetails = useForm<any>({
    initialState: selectedRowData,
    isBulk: false,
    validations: formValidators,
  });

  const inputElements = [
    {
      name: "name",
      label: businessUnitsLang.inputTitle,
      variant: "text",
      options: centerCodesOptions,
      // style: { flex: "40%" },
      placeholder: businessUnitsLang.inputTitle,
    },
    {
      name: "costCenter",
      label: languageConfig.tenants.costCenter.title,
      variant: "select",
      options: centerCodesOptions,
      // style: { flex: "40%" },
      placeholder: languageConfig.tenants.costCenter.title,
    },
    {
      name: "description",
      label: descriptionLable,
      variant: "text",
      // style: { flex: "100%" },
      placeholder: descriptionLable,
      rows: 1,
      allowEmpty: true,
    },
  ];
  const editFormConfig = {
    nextButtonText: "Save",
    onNextClick: handleEditDetailsClick,
    formTitle: businessUnitsLang.editBusinessUnit,
  };

  const addFormConfig = {
    nextButtonText: "Create",
    onNextClick: handleAddDetailsClick,
    formTitle: businessUnitsLang.addBusinessUnit,
    addButtonText: businessUnitsLang.addBusinessUnit,
  };

  const deleteFormConfig = {
    nextButtonText: "Delete",
    onNextClick: handleDeleteConfirmed,
    formTitle: businessUnitsLang.deleteBusinessUnit,
    getQuestion: (rowIndex: number) =>
      `Are you sure you want to delete this ${defaultFormState[rowIndex]?.name} ${businessUnitsLang.inputTitle}?`,
  };

  const formConfig = {
    editFormConfig,
    addFormConfig,
    deleteFormConfig,
    tableHeaderTitle: businessUnitsLang.title,
  };
  const columns = [
    { accessorKey: "name", header: "Name" },
    { accessorKey: "costCenter", header: languageConfig.tenants.costCenter.title },
    { accessorKey: "description", header: descriptionLable },
  ];

  const selectOptions = { costCenter: centerCodesOptions };
  const readOnlyFields = {
    // name: true,
    costCenter: true,
  };
  return (
    <CrudTable
      isLoading={isLoading || businessUnitsLoading}
      formConfig={formConfig}
      selectOptions={selectOptions}
      defaultFormState={defaultFormState}
      formValidators={formValidators}
      inputElements={inputElements}
      rowAdditionaInitialValues={rowAdditionaInitialValues}
      columns={columns}
      useFormDetails={useFormDetails}
      setSelectedRow={setSelectedRow}
      selectedRow={selectedRow}
      readOnlyFields={readOnlyFields}
    />
  );
};
