import React, { useEffect, useMemo, useState } from "react";

import { useQuery } from "@tanstack/react-query";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import { CrudTable } from "src/modules/Settings/components/Common/CrudTable";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";

const {
  jobFamilies: jobFamilyLang,
  departments: departmentsLang,
  businessUnits: businessUnitsLang,
} = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const getRequestPayload = (formDetail: BaseObject) => {
  return {
    business_unit: formDetail.businessUnits,
    name: formDetail.jobFamily,
    department: formDetail.department,
  };
};

const rowAdditionaInitialValues = {
  businessUnits: "",
  department: "",
  jobFamily: "",
};

export const JobFamily = () => {
  const tenantId = getCurrentTenantId();
  const [departmentList, setDepartmentList] = useState<BaseObject[]>([]);

  const { data: businessUnits, isLoading: businessUnitsLoading } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const {
    data: jobFamilyList,
    isLoading: jobFamilyListLoading,
    refetch: jobFamilyListRefetch,
  } = useQuery(["get-all-job-families"], async () => departmentService.getAllJobFamilies(), {
    enabled: !!tenantId,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  const { data: allDepartments, isLoading: allDepartmentsLoading } = useQuery(
    ["get-all-departments"],
    async () => departmentService.getAllDepartments(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const businessUnitsOptions = convertListToOptions(businessUnits as [], "name", "name");
  const deafaultResponse =
    jobFamilyList?.map((jobFamily: BaseObject) => ({
      businessUnits: jobFamily.business_unit,
      department: jobFamily.department,
      jobFamily: jobFamily.name,
    })) || [];

  const formValidators = {
    businessUnits: [validators.validateInput],
    department: [validators.validateInput],
    jobFamily: [validators.validateInput],
  };

  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const selectedRowData = useMemo(
    () => (selectedRow !== null ? deafaultResponse?.[selectedRow] : rowAdditionaInitialValues),
    [selectedRow],
  );

  const useFormDetails = useForm<any>({
    initialState: selectedRowData,
    isBulk: false,
    validations: formValidators,
  });

  useEffect(() => {
    const departments: BaseObject[] =
      allDepartments?.filter((department) => department.business_unit === useFormDetails?.formDetails.businessUnits) ||
      [];
    const depatmentOptions = convertListToOptions(departments, "name", "name");
    setDepartmentList(depatmentOptions as unknown as BaseObject[]);
  }, [useFormDetails?.formDetails]);

  useEffect(() => {
    const { department } = useFormDetails?.formDetails || {};
    const isOptionAllowed = department?.length == 0 || departmentList?.length == 0;

    if (!isOptionAllowed) {
      useFormDetails?.setFormDetail("department", []);
    }
  }, [useFormDetails?.formDetails?.businessUnits]);

  const inputElements = [
    {
      name: "businessUnits",
      label: businessUnitsLang.inputTitle,
      variant: "select",
      // style: { flex: "30%" },
      placeholder: businessUnitsLang.inputTitle,
    },
    {
      name: "department",
      label: departmentsLang.inputTitle,
      variant: "select",
      // style: { flex: "30%" },
      placeholder: departmentsLang.inputTitle,
      isDynamicOptions: true,
    },
    {
      name: "jobFamily",
      label: jobFamilyLang.inputTitle,
      variant: "text",
      // style: { flex: "30%" },
      placeholder: jobFamilyLang.inputTitle,
      isEditable: true,
    },
  ];
  const postFormSubmit = () => {
    jobFamilyListRefetch();
  };

  const handleEditDetailsClick = async (formDetails: BaseObject, selectedIndex: number) => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = {
      new_name: parsedData.name,
      name: jobFamilyList?.[selectedIndex]?.name,
      business_unit: parsedData?.business_unit,
      department: parsedData?.department,
    };
    await departmentService.updateJobFamilyDetails(requestObject);
    postFormSubmit();
  };

  const handleAddDetailsClick = async (formDetails: BaseObject) => {
    const payload = getRequestPayload(formDetails);
    await departmentService.setJobFamilyDetails([payload]);
    postFormSubmit();
  };

  const handleDeleteConfirmed = async (index: number) => {
    await departmentService.deleteJobFamilyDetails(jobFamilyList?.[index] || {});
    postFormSubmit();
  };

  const editFormConfig = {
    nextButtonText: "Save",
    onNextClick: handleEditDetailsClick,
    formTitle: "",
  };

  const addFormConfig = {
    nextButtonText: "Create",
    onNextClick: handleAddDetailsClick,
    formTitle: "Job Families",
    addButtonText: "Add Job Family",
  };

  const deleteFormConfig = {
    nextButtonText: "Delete",
    onNextClick: handleDeleteConfirmed,
    formTitle: jobFamilyLang.deleteJobFamily,
    getQuestion: (rowIndex: number) =>
      `Are you sure you want to delete this ${deafaultResponse[rowIndex]?.jobFamily} ${jobFamilyLang.inputTitle}?`,
  };
  const formConfig = {
    editFormConfig,
    addFormConfig,
    deleteFormConfig,
    tableHeaderTitle: jobFamilyLang.title,
  };
  const columns = [
    { accessorKey: "jobFamily", header: "Name" },
    { accessorKey: "department", header: "Department" },
    { accessorKey: "businessUnits", header: "Business Unit" },
  ];
  const selectOptions: BaseObject = { businessUnits: businessUnitsOptions, department: departmentList };
  const readOnlyFields = {
    businessUnits: true,
    department: true,
  };

  return (
    <React.Fragment>
      <CrudTable
        isLoading={businessUnitsLoading || jobFamilyListLoading || allDepartmentsLoading}
        formConfig={formConfig}
        selectOptions={selectOptions}
        defaultFormState={deafaultResponse}
        formValidators={formValidators}
        inputElements={inputElements}
        rowAdditionaInitialValues={rowAdditionaInitialValues}
        columns={columns}
        useFormDetails={useFormDetails}
        setSelectedRow={setSelectedRow}
        selectedRow={selectedRow}
        readOnlyFields={readOnlyFields}
      />
    </React.Fragment>
  );
};
