import React, { useEffect, useMemo, useState } from "react";

import { useQuery } from "@tanstack/react-query";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import { CrudTable } from "src/modules/Settings/components/Common/CrudTable";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";

const {
  button: { saveTitle },
  jobTitles: jobTitleLang,
  departments: departmentsLang,
  businessUnits: businessUnitsLang,
  workRole: workRoleLang,
} = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const getRequestPayload = (formDetail: BaseObject) => {
  return {
    business_unit: formDetail.businessUnits,
    name: formDetail.jobTitle,
    department: formDetail.department,
    // ...splitWorkRole(formDetail.workRole as string),
    work_role: formDetail.workRole,
  };
};

const constructWorkRoleOptions = (workRoleList?: BaseObject[]) => {
  if (workRoleList && workRoleList?.length > 0) {
    const result = workRoleList.map((item) => {
      return {
        value: item.name,
        label: item.name,
      };
    });
    return result;
  }
  return [];
};

const rowAdditionaInitialValues = {
  businessUnits: "",
  department: "",
  jobTitle: "",
  workRole: "",
};

export const JobTitle = () => {
  const tenantId = getCurrentTenantId();
  const [departmentList, setDepartmentList] = useState<BaseObject[]>([]);

  const { data: businessUnits, isLoading: businessUnitsLoading } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const {
    data: jobTitleList,
    isLoading: jobTitleListLoading,
    refetch: jobTitleListRefetch,
  } = useQuery(["get-all-job-titles"], async () => departmentService.getAllJobTitles(), {
    enabled: !!tenantId,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  const { data: allDepartments, isLoading: allDepartmentsLoading } = useQuery(
    ["get-all-departments"],
    async () => departmentService.getAllDepartments(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const { data: workRoleList } = useQuery(
    ["get-all-work-roles"],
    async () => departmentService.getAllWorkRoles(tenantId),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  useEffect(() => {
    const departmentList = jobTitleList?.map((jobTitle) => {
      const departments = allDepartments?.filter((department) => department.business_unit === jobTitle.business_unit);
      return convertListToOptions(departments as BaseObject[], "name", "name");
    });
    setDepartmentList(departmentList as unknown as BaseObject[]);
  }, [allDepartments, jobTitleList]);

  const businessUnitsOptions = useMemo(
    () => convertListToOptions(businessUnits as [], "name", "name"),
    [businessUnits],
  );
  const deafaultResponse =
    jobTitleList?.map((jobTitle: BaseObject) => ({
      businessUnits: jobTitle.business_unit,
      department: jobTitle.department,
      jobTitle: jobTitle.name,
      workRole: jobTitle?.work_role_name,
    })) || [];

  const formValidators = {
    businessUnits: [validators.validateInput],
    department: [validators.validateInput],
    jobTitle: [validators.validateInput],
    workRole: [validators.validateInput],
  };

  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const selectedRowData = useMemo(
    () => (selectedRow !== null ? deafaultResponse?.[selectedRow] : rowAdditionaInitialValues),
    [selectedRow],
  );

  const useFormDetails = useForm<any>({
    initialState: selectedRowData,
    isBulk: false,
    validations: formValidators,
  });

  useEffect(() => {
    const departments: BaseObject[] =
      allDepartments?.filter((department) => department.business_unit === useFormDetails?.formDetails.businessUnits) ||
      [];
    const depatmentOptions = convertListToOptions(departments, "name", "name");
    setDepartmentList(depatmentOptions as unknown as BaseObject[]);
  }, [useFormDetails?.formDetails]);

  useEffect(() => {
    const { department } = useFormDetails?.formDetails || {};
    const isOptionAllowed = department?.length == 0 || departmentList?.length == 0;

    if (!isOptionAllowed && selectedRow === null) {
      useFormDetails?.setFormDetail("department", []);
    }
  }, [useFormDetails?.formDetails?.businessUnits]);

  const inputElements = [
    {
      name: "businessUnits",
      label: businessUnitsLang.inputTitle,
      variant: "select",
      // style: { flex: "30%" },
      placeholder: businessUnitsLang.inputTitle,
    },
    {
      name: "department",
      label: departmentsLang.inputTitle,
      variant: "select",
      // style: { flex: "30%" },
      placeholder: departmentsLang.inputTitle,
    },
    {
      name: "workRole",
      label: workRoleLang.inputTitle,
      variant: "select",
      // style: { flex: "30%" },
      placeholder: workRoleLang.inputTitle,
    },
    {
      name: "jobTitle",
      label: jobTitleLang.inputTitle,
      variant: "text",
      // style: { flex: "0 0 31%" },
      placeholder: jobTitleLang.inputTitle,
    },
  ];
  const postFormSubmit = () => {
    jobTitleListRefetch();
  };

  const handleEditDetailsClick = async (formDetails: BaseObject, selectedIndex: number) => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = {
      ...parsedData,
      new_name: parsedData.name,
      name: jobTitleList?.[selectedIndex]?.name,
    };
    await departmentService.updateJobTitleDetails(requestObject);
    postFormSubmit();
  };

  const handleAddDetailsClick = async (formDetails: BaseObject) => {
    const payload = getRequestPayload(formDetails);
    await departmentService.setJobTitleDetails([payload]);
    postFormSubmit();
  };

  const handleDeleteConfirmed = async (index: number) => {
    await departmentService.deleteJobTitleDetails(jobTitleList?.[index] || {});
    postFormSubmit();
  };

  const editFormConfig = {
    nextButtonText: saveTitle,
    onNextClick: handleEditDetailsClick,
    formTitle: "",
  };

  const addFormConfig = {
    nextButtonText: "Create",
    onNextClick: handleAddDetailsClick,
    formTitle: jobTitleLang.inputTitle,
    addButtonText: jobTitleLang.addJobTitle,
  };

  const deleteFormConfig = {
    nextButtonText: "Delete",
    onNextClick: handleDeleteConfirmed,
    formTitle: jobTitleLang.inputTitle,
    getQuestion: (rowIndex: number) =>
      `Are you sure you want to delete this ${deafaultResponse[rowIndex]?.jobTitle} ${jobTitleLang.inputTitle}?`,
  };

  const formConfig = {
    editFormConfig,
    addFormConfig,
    deleteFormConfig,
    tableHeaderTitle: jobTitleLang.title,
  };

  const columns = [
    { accessorKey: "jobTitle", header: "Name" },
    { accessorKey: "workRole", header: workRoleLang.inputTitle },
    { accessorKey: "department", header: departmentsLang.inputTitle },
    { accessorKey: "businessUnits", header: businessUnitsLang.inputTitle },
  ];

  const selectOptions: BaseObject = useMemo(
    () => ({
      businessUnits: businessUnitsOptions,
      department: departmentList,
      workRole: constructWorkRoleOptions(workRoleList),
    }),
    [departmentList],
  );

  const readOnlyFields = {
    businessUnits: true,
    department: true,
    workRole: true,
  };

  return (
    <CrudTable
      isLoading={businessUnitsLoading || jobTitleListLoading || allDepartmentsLoading}
      formConfig={formConfig}
      selectOptions={selectOptions}
      defaultFormState={deafaultResponse}
      formValidators={formValidators}
      inputElements={inputElements}
      rowAdditionaInitialValues={rowAdditionaInitialValues}
      columns={columns}
      useFormDetails={useFormDetails}
      setSelectedRow={setSelectedRow}
      selectedRow={selectedRow}
      readOnlyFields={readOnlyFields}
    />
  );
};
