import React, { useEffect, useMemo, useState } from "react";

import { useQuery } from "@tanstack/react-query";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import { CrudTable } from "src/modules/Settings/components/Common/CrudTable";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import tenantsService from "src/services/tenants.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";

const {
  button: { createTitle, saveTitle },
  teams: teamLang,
  departments: departmentsLang,
  businessUnits: businessUnitsLang,
} = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: Record<string, unknown>[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const getRequestPayload = (formDetail: BaseObject) => {
  return {
    business_unit: formDetail.businessUnits,
    name: formDetail.team,
    department: formDetail.department,
  };
};

const rowAdditionaInitialValues = {
  businessUnits: "",
  department: "",
  team: "",
};

export const Team = () => {
  const tenantId = getCurrentTenantId();
  const [departmentList, setDepartmentList] = useState<BaseObject[]>([]);

  const { data: businessUnits, isLoading: businessUnitsLoading } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const {
    data: teamList,
    isLoading: teamListLoading,
    refetch: teamListRefetch,
  } = useQuery(["get-all-teams"], async () => departmentService.getAllTeams(), {
    enabled: !!tenantId,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  const { data: allDepartments, isLoading: allDepartmentsLoading } = useQuery(
    ["get-all-departments"],
    async () => departmentService.getAllDepartments(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  tenantsService.getWorkRoleDetails(getCurrentTenantId());

  const businessUnitsOptions = convertListToOptions(businessUnits as [], "name", "name");
  const deafaultResponse =
    teamList?.map((team) => ({
      businessUnits: team.business_unit,
      department: team.department,
      team: team.name,
    })) || [];

  const formValidators = {
    businessUnits: [validators.validateInput],
    department: [validators.validateInput],
    team: [validators.validateInput],
  };

  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const selectedRowData = useMemo(
    () => (selectedRow !== null ? deafaultResponse?.[selectedRow] : rowAdditionaInitialValues),
    [selectedRow],
  );

  const useFormDetails = useForm<any>({
    initialState: selectedRowData,
    isBulk: false,
    validations: formValidators,
  });

  useEffect(() => {
    const departments: BaseObject[] =
      allDepartments?.filter((department) => department.business_unit === useFormDetails.formDetails.businessUnits) ||
      [];
    const depatmentOptions = convertListToOptions(departments, "name", "name");
    setDepartmentList(depatmentOptions as unknown as BaseObject[]);
  }, [useFormDetails.formDetails]);

  useEffect(() => {
    const { department } = useFormDetails?.formDetails || {};
    const isOptionAllowed = department?.length == 0 || departmentList?.length == 0;

    if (!isOptionAllowed) {
      useFormDetails?.setFormDetail("department", []);
    }
  }, [useFormDetails?.formDetails?.businessUnits]);

  const inputElements = [
    {
      name: "businessUnits",
      label: businessUnitsLang.inputTitle,
      variant: "select",
      // style: { flex: "30%" },
      placeholder: businessUnitsLang.inputTitle,
    },
    {
      name: "department",
      label: departmentsLang.inputTitle,
      variant: "select",
      // style: { flex: "30%" },
      placeholder: departmentsLang.inputTitle,
    },
    {
      name: "team",
      label: teamLang.inputTitle,
      variant: "text",
      // style: { flex: "30%" },
      placeholder: teamLang.inputTitle,
    },
  ];

  const postFormSubmit = () => {
    teamListRefetch();
  };

  const handleEditDetailsClick = async (formDetails: BaseObject, selectedIndex: number) => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = {
      new_name: parsedData.name,
      name: teamList?.[selectedIndex]?.name,
      business_unit: parsedData?.business_unit,
      department: parsedData?.department,
    };
    await departmentService.updateTeamDetails(requestObject);
    postFormSubmit();
  };

  const handleAddDetailsClick = async (formDetails: BaseObject) => {
    const payload = getRequestPayload(formDetails);
    await departmentService.setTeamDetails([payload]);
    postFormSubmit();
  };

  const handleDeleteConfirmed = async (index: number) => {
    await departmentService.deleteTeamDetails(teamList?.[index] || {});
    postFormSubmit();
  };

  const editFormConfig = {
    nextButtonText: saveTitle,
    onNextClick: handleEditDetailsClick,
    formTitle: "",
  };

  const addFormConfig = {
    nextButtonText: createTitle,
    onNextClick: handleAddDetailsClick,
    formTitle: teamLang.inputTitle,
    addButtonText: teamLang.addTeam,
  };

  const deleteFormConfig = {
    nextButtonText: "Delete",
    onNextClick: handleDeleteConfirmed,
    formTitle: teamLang.inputTitle,
    getQuestion: (rowIndex: number) =>
      `Are you sure you want to delete this ${deafaultResponse[rowIndex]?.team} ${teamLang.inputTitle}?`,
  };

  const formConfig = {
    editFormConfig,
    addFormConfig,
    deleteFormConfig,
    tableHeaderTitle: teamLang.title,
  };

  const selectOptions = { businessUnits: businessUnitsOptions, department: departmentList };
  const columns = [
    { accessorKey: "team", header: "Name" },
    { accessorKey: "department", header: departmentsLang.inputTitle },
    { accessorKey: "businessUnits", header: businessUnitsLang.inputTitle },
  ];

  const readOnlyFields = {
    businessUnits: true,
    department: true,
  };

  return (
    <React.Fragment>
      <CrudTable
        isLoading={businessUnitsLoading || teamListLoading || allDepartmentsLoading}
        formConfig={formConfig}
        selectOptions={selectOptions}
        defaultFormState={deafaultResponse}
        formValidators={formValidators}
        inputElements={inputElements}
        rowAdditionaInitialValues={rowAdditionaInitialValues}
        columns={columns}
        useFormDetails={useFormDetails}
        setSelectedRow={setSelectedRow}
        selectedRow={selectedRow}
        readOnlyFields={readOnlyFields}
      />
    </React.Fragment>
  );
};
