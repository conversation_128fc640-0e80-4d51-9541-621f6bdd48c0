import React, { useMemo, useState } from "react";

import { useQuery } from "@tanstack/react-query";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import { CrudTable } from "src/modules/Settings/components/Common/CrudTable";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";

const { departments: departmentsLang, businessUnits: businessUnitsLang } = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const getRequestPayload = (formDetail: BaseObject) => {
  return {
    business_unit: formDetail.businessUnits,
    name: formDetail.department,
  };
};

const rowAdditionaInitialValues = {
  businessUnits: "",
  department: "",
};

export const Departments = () => {
  const tenantId = getCurrentTenantId();
  const {
    data: departmentList,
    isLoading: isDepartmentLoading,
    refetch: refetchDepartment,
  } = useQuery(["get-all-departments"], async () => departmentService.getAllDepartments(), {
    enabled: !!tenantId,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  const { data: businessUnits, isLoading: businessUnitsLoading } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const businessUnitsOptions = convertListToOptions(businessUnits as [], "name", "name");
  const deafaultResponse =
    departmentList?.map((businessUnit: BaseObject) => ({
      businessUnits: businessUnit.business_unit,
      department: businessUnit.name,
    })) || [];

  const defaultFormState: BaseObject[] = deafaultResponse;

  const formValidators = {
    businessUnits: [validators.validateInput],
    department: [validators.validateInput],
  };

  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const selectedRowData = useMemo(
    () => (selectedRow !== null ? deafaultResponse?.[selectedRow] : rowAdditionaInitialValues),
    [selectedRow],
  );

  const useFormDetails = useForm<any>({
    initialState: selectedRowData,
    isBulk: false,
    validations: formValidators,
  });

  const inputElements = [
    {
      name: "businessUnits",
      label: businessUnitsLang.inputTitle,
      variant: "select",
      // style: { flex: "40%" },
      placeholder: businessUnitsLang.inputTitle,
    },
    {
      name: "department",
      label: departmentsLang.inputTitle,
      variant: "text",
      // style: { flex: "40%" },
      placeholder: departmentsLang.inputTitle,
    },
  ];

  const postFormSubmit = () => {
    refetchDepartment();
  };

  const handleEditDetailsClick = async (formDetails: BaseObject, selectedIndex: number) => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = {
      new_name: parsedData.name,
      name: departmentList?.[selectedIndex]?.name,
      business_unit: parsedData?.business_unit,
    };
    await departmentService.updateDepartmentDetails(requestObject);
    postFormSubmit();
  };

  const handleAddDetailsClick = async (formDetails: BaseObject) => {
    const payload = getRequestPayload(formDetails);
    await departmentService.setDepartmentDetails([payload]);
    postFormSubmit();
  };

  const handleDeleteConfirmed = async (index: number) => {
    await departmentService.deleteDepartmentDetails(departmentList?.[index] || {});
    postFormSubmit();
  };

  const editFormConfig = {
    nextButtonText: "Save",
    onNextClick: handleEditDetailsClick,
    formTitle: departmentsLang.editDepartment,
  };

  const addFormConfig = {
    nextButtonText: "Create",
    onNextClick: handleAddDetailsClick,
    formTitle: departmentsLang.title,
    addButtonText: departmentsLang.addDepartment,
  };

  const deleteFormConfig = {
    nextButtonText: "Delete",
    onNextClick: handleDeleteConfirmed,
    formTitle: departmentsLang.deleteDepartment,
    getQuestion: (rowIndex: number) =>
      `Are you sure you want to delete this ${defaultFormState[rowIndex]?.department} ${departmentsLang.inputTitle}?`,
  };

  const formConfig = {
    editFormConfig,
    addFormConfig,
    deleteFormConfig,
    tableHeaderTitle: departmentsLang.title,
  };

  const columns = [
    { accessorKey: "department", header: "Name" },
    { accessorKey: "businessUnits", header: businessUnitsLang.inputTitle },
  ];

  const selectOptions = { businessUnits: businessUnitsOptions };
  const readOnlyFields = {
    businessUnits: true,
  };

  return (
    <CrudTable
      isLoading={isDepartmentLoading || businessUnitsLoading}
      selectOptions={selectOptions}
      defaultFormState={defaultFormState}
      formValidators={formValidators}
      inputElements={inputElements}
      rowAdditionaInitialValues={rowAdditionaInitialValues}
      columns={columns}
      formConfig={formConfig}
      useFormDetails={useFormDetails}
      setSelectedRow={setSelectedRow}
      selectedRow={selectedRow}
      readOnlyFields={readOnlyFields}
    />
  );
};
