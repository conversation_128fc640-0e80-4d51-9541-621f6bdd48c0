import { Grid2 } from "@mui/material";
import React from "react";

type GridSizeProps =
  | number
  | {
      xs?: number;
      sm?: number;
      md?: number;
      lg?: number;
      xl?: number;
    };

type EffiInputRendererProps = {
  form: any;
  fieldProps: any;
  formProps: any;
  containerProps?: {
    size?: number | GridSizeProps;
  };
};

const EffiInputRenderer: React.FC<EffiInputRendererProps> = ({
  form,
  fieldProps,
  formProps,
  containerProps = { size: 4 },
}) => {
  if (formProps.hidden) {
    return null;
  }
  if (!formProps.type) {
    throw new Error("Required: Please provide the type of input");
  }

  switch (formProps.type) {
    case "select":
      return (
        <Grid2 size={containerProps.size}>
          <form.AppField name={fieldProps.name} {...fieldProps}>
            {(field: any) => (
              <field.EffiSelect
                label={formProps.label}
                required={formProps.required}
                options={fieldProps.options}
                {...formProps}
              />
            )}
          </form.AppField>
        </Grid2>
      );
    case "multi-select":
      return (
        <Grid2 size={containerProps.size}>
          <form.AppField name={fieldProps.name} {...fieldProps}>
            {(field: any) => (
              <field.EffiMultiSelect
                label={formProps.label}
                required={formProps.required}
                options={fieldProps.options}
                {...formProps}
              />
            )}
          </form.AppField>
        </Grid2>
      );
    case "date":
      return (
        <Grid2 size={containerProps.size}>
          <form.AppField name={fieldProps.name} {...fieldProps}>
            {(field: any) => <field.EffiDate label={formProps.label} required={formProps.required} {...formProps} />}
          </form.AppField>
        </Grid2>
      );
    case "phone":
      return (
        <Grid2 size={containerProps.size}>
          <form.AppField name={fieldProps.name} {...fieldProps}>
            {(field: any) => <field.EffiPhone label={formProps.label} required={formProps.required} {...formProps} />}
          </form.AppField>
        </Grid2>
      );
    case "switch":
      return (
        <Grid2 size={containerProps.size}>
          <form.AppField name={fieldProps.name} {...fieldProps}>
            {(field: any) => <field.EffiSwitch {...formProps} />}
          </form.AppField>
        </Grid2>
      );
    case "currency":
      return (
        <Grid2 size={containerProps.size}>
          <form.AppField name={fieldProps.name} {...fieldProps}>
            {(field: any) => <field.EffiCurrency {...formProps} />}
          </form.AppField>
        </Grid2>
      );
    case "percentage":
      return (
        <Grid2 size={containerProps.size}>
          <form.AppField name={fieldProps.name} {...fieldProps}>
            {(field: any) => <field.EffiPercentageField {...formProps} />}
          </form.AppField>
        </Grid2>
      );
    case "radio-group":
      return (
        <Grid2 size={containerProps.size}>
          <form.AppField name={fieldProps.name} {...fieldProps}>
            {(field: any) => (
              <field.EffiRadioGroup
                label={formProps.label}
                required={formProps.required}
                options={fieldProps.options}
                layout={formProps.layout}
                size={formProps.size}
                {...formProps}
              />
            )}
          </form.AppField>
        </Grid2>
      );
    case "checkbox":
      return (
        <Grid2 size={containerProps.size}>
          <form.AppField name={fieldProps.name} {...fieldProps}>
            {(field: any) => (
              <field.EffiCheckbox label={formProps.label} required={formProps.required} {...formProps} />
            )}
          </form.AppField>
        </Grid2>
      );
    case "radio-group-with-checkbox":
      return (
        <Grid2 size={containerProps.size}>
          <form.AppField name={fieldProps.name} {...fieldProps}>
            {(field: any) => (
              <field.EffiRadioGroupWithCheckbox
                label={formProps.label}
                checkbox_label={formProps.checkbox_label}
                required={formProps.required}
                options={fieldProps.options}
                layout={formProps.layout}
                size={formProps.size}
                {...formProps}
              />
            )}
          </form.AppField>
        </Grid2>
      );
    default:
      return (
        <Grid2 size={containerProps.size}>
          <form.AppField name={fieldProps.name} {...fieldProps}>
            {(field: any) => (
              <field.EffiTextField label={formProps.label} required={formProps.required} {...formProps} />
            )}
          </form.AppField>
        </Grid2>
      );
  }
};

export default EffiInputRenderer;
