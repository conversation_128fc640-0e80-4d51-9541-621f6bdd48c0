import { Box, FormControl, TextField } from "@mui/material";
import React from "react";
import { useFieldContext } from "src/modules/Common/Form/effiFormContext";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";

export type EffiTextFieldProps = {
  label: string;
  required?: boolean;
  size?: "small" | "medium";
  [key: string]: any;
};

const EffiTextField: React.FC<EffiTextFieldProps> = ({ label, required, size = "small", ...otherProps }) => {
  const field = useFieldContext();
  return (
    <Box display="flex" flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} data-testId={field.name} />
      <FormControl error={field.state?.meta?.errors?.length > 0}>
        <TextField
          name={field.name}
          id={field.name}
          fullWidth
          data-testId={field.name}
          value={otherProps?.type === "number" ? Number(field.state.value) : field.state.value}
          onChange={(e) => field.handleChange(otherProps?.type === "number" ? Number(e.target.value) : e.target.value)}
          error={field.state?.meta?.errors?.length > 0}
          helperText={field.state?.meta?.errors?.map((err) => err.message)}
          size={size}
          {...otherProps}
        />
      </FormControl>
    </Box>
  );
};

export default EffiTextField;
