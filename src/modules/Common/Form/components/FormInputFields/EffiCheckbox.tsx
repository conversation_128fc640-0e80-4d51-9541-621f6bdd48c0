import { Box, Checkbox, FormControlLabel, Typography } from "@mui/material";
import React from "react";
import { useFieldContext } from "../../effiFormContext";

type EffiCheckboxProps = {
  label: string;
  required?: boolean;
  size?: "small" | "medium";
};

const EffiCheckbox: React.FC<EffiCheckboxProps> = ({ label, required, size = "small", ...otherProps }) => {
  const field = useFieldContext();
  return (
    <Box display="flex" flexDirection="column">
      <FormControlLabel
        label={
          <Typography variant="body2" component="span">
            {label}
          </Typography>
        }
        required={required}
        disabled={otherProps?.disabled}
        control={
          <Checkbox
            id={field.name}
            name={field.name}
            data-testId={field.name}
            checked={field.state.value as boolean}
            onChange={(_ev, checked) => field.handleChange(checked)}
            size={size}
            {...otherProps}
          />
        }
      />
    </Box>
  );
};

export default EffiCheckbox;
