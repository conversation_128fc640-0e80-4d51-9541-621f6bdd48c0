import { Box, FormControl, FormHelperText, MenuItem, Select } from "@mui/material";
import React from "react";
import { Option } from "src/app/global";
import { useFieldContext } from "src/modules/Common/Form/effiFormContext";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";

type EffiSelectProps = {
  label: string;
  required?: boolean;
  options: Option<string, string | number>[];
  helperText?: string;
  size?: "small" | "medium";
};

const MappedOptionItem = (option: Option<string, string | number>) => {
  return (
    <MenuItem value={option.value} key={option.value} disabled={option.disabled}>
      {option.label}
    </MenuItem>
  );
};

const EffiSelect: React.FC<EffiSelectProps> = ({
  label,
  required,
  options = [],
  helperText,
  size = "small",
  ...otherProps
}) => {
  const field = useFieldContext();
  return (
    <Box display="flex" flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} />
      <FormControl error={field.state?.meta?.errors?.length > 0}>
        <Select
          name={field.name}
          id={field.name}
          data-testId={field.name}
          value={field.state.value}
          onChange={(e) => field.handleChange(e.target.value)}
          size={size}
          fullWidth
          {...otherProps}
        >
          {options?.map(MappedOptionItem)}
        </Select>
        <FormHelperText title={helperText} />
      </FormControl>
    </Box>
  );
};

export default EffiSelect;
