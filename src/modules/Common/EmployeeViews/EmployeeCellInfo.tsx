import { Avatar, Box, Typography } from "@mui/material";
import React from "react";

interface EmployeeCellInfoProps {
  name?: string;
  jobTitle?: string;
  employeeCode?: string;
  displayPic?: string;
  hideAvatar?: boolean;
}

export const EmployeeCellInfo: React.FC<EmployeeCellInfoProps> = ({
  name,
  jobTitle,
  employeeCode,
  displayPic,
  hideAvatar = false,
}) => {
  return (
    <Box display="flex" gap={1} alignItems="center" width={"max-content"}>
      {!hideAvatar && <Avatar alt={name} src={displayPic || "#"} />}
      <Box display="flex" flexDirection="column">
        <Typography fontSize={14}>{name}</Typography>
        <Typography fontSize={12} color="#667085">
          {jobTitle}
        </Typography>
        {employeeCode && (
          <Typography fontSize={12} color="#667085">
            {employeeCode}
          </Typography>
        )}
      </Box>
    </Box>
  );
};
