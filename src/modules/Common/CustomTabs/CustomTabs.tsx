import { AppBar, Box, Tab, Tabs, TabsOwnProps, styled } from "@mui/material";
import React, { useEffect, useState } from "react";

const CustomTab = styled(Tab)(() => ({
  background: "#E6EFEF",
  color: "#404958 !important",
  textTransform: "none",
  margin: "10px 10px",
  "&.Mui-selected": {
    background: "white",
    margin: "10px 10px",
    color: "#005F56 !important",
    fontWeight: 500,
    textTransform: "none",
    borderRadius: 8,
    textDecoration: "none",
  },
}));

const CustomTabs = styled(Tabs)(() => ({
  "&.MuiTabs-indicator": {
    display: "none",
  },
}));

const ComponentContainer = styled(Box)(() => ({
  margin: "20px 0px",
  // height: "100%",
  // maxHeight: "calc(100vh - 200px)",
  flex: 1,
  overflow: "auto",
}));

export type TabType = {
  id: string | number;
  component: React.ReactNode;
  label: string;
};

export type Props = TabsOwnProps & {
  tabs: TabType[];
  currentTabIndex?: number;
  handleTabChange?: (currentTabIndex: number) => void;
  hideTabBar?: boolean;
  containerStyle?: React.CSSProperties;
  componentContainerStyle?: React.CSSProperties;
};

const TabsView = ({
  tabs,
  handleTabChange,
  currentTabIndex = 0,
  hideTabBar = false,
  containerStyle = {},
  componentContainerStyle = {},
  ...others
}: Props) => {
  const [currentTab, setCurrentTab] = useState<number>(currentTabIndex);

  const handleChange = (__event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
    handleTabChange?.(newValue);
  };

  useEffect(() => {
    setCurrentTab(currentTabIndex);
  }, [currentTabIndex]);

  return (
    <Box sx={{ width: "100%", display: "flex", flexDirection: "column", ...containerStyle }}>
      {!hideTabBar && (
        <AppBar
          position="static"
          elevation={0}
          sx={{
            background: "#E6EFEF",
            borderRadius: 2,
          }}
        >
          <CustomTabs
            value={currentTab}
            onChange={handleChange}
            textColor="primary"
            TabIndicatorProps={{
              style: {
                display: "none",
                transition: "0.3s ease-in",
              },
            }}
            aria-label="full width tabs example"
            {...others}
          >
            {Object.values(tabs).map((tab) => (
              <CustomTab key={tab.id} label={tab.label} style={{ minHeight: "36px", padding: "0 16px" }} />
            ))}
          </CustomTabs>
        </AppBar>
      )}
      <ComponentContainer sx={componentContainerStyle}>{tabs[currentTab].component}</ComponentContainer>
    </Box>
  );
};

export default TabsView;
