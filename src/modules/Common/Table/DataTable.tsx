import { MaterialReactTable, MaterialReactTableProps } from "material-react-table";
/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";

const DataTable = <T extends Record<string, any>>({
  data = [],
  columns,
  enablePagination = false,
  maxHeightDeduction = 300,
  ...otherProps
}: MaterialReactTableProps<T> & { maxHeightDeduction?: number }) => {
  const { contentHeight } = useAppSelector((state) => state.app);

  return (
    <MaterialReactTable
      columns={columns}
      data={data}
      enableStickyHeader={true}
      enableColumnActions={false}
      enableCellActions={false}
      enableSorting={false}
      enableBottomToolbar={false}
      enableTopToolbar={false}
      enablePagination={enablePagination}
      initialState={{ showGlobalFilter: true }}
      positionGlobalFilter="left"
      muiTableHeadCellProps={{
        sx: {
          fontSize: "13px",
          fontWeight: 600,
          textTransform: "uppercase",
          letterSpacing: "0.5px",
          color: "rgba(55, 65, 81, 0.8)", // Tailwind's gray-700 approx
        },
      }}
      muiTableContainerProps={{
        sx: {
          height: contentHeight,
          maxHeight: contentHeight - maxHeightDeduction,
        },
      }}
      muiTableHeadRowProps={{
        sx: {
          background: "#F8FFFE",
        },
      }}
      muiSearchTextFieldProps={{
        InputLabelProps: { shrink: true, disableAnimation: true },
        InputProps: {
          sx: {
            borderRadius: 4,
          },
        },
        size: "small",
        placeholder: "Search all rows",
        variant: "outlined",
        type: "search",
      }}
      muiTablePaperProps={{
        elevation: 0,
        sx: {
          borderRadius: 4,
          border: "1px solid #e0e0e0",
        },
      }}
      muiFilterTextFieldProps={{
        sx: {
          borderRadius: 4,
          "& .MuiInputBase-root": {
            borderRadius: "50px",
            border: "1px #EDEDED solid",
          },
        },
      }}
      {...(otherProps as any)}
    />
  );
};

export default DataTable;
