import { Delete, Edit, VisibilityOutlined } from "@mui/icons-material";
import { Box, IconButton, Tooltip } from "@mui/material";
import React from "react";

type ActionOverrides = {
  onClick?: () => void;
  title?: string;
  tooltip?: string;
  Icon?: JSX.ElementType;
  disabled?: boolean;
  hide?: boolean | false;
  [key: string]: string | boolean | undefined | unknown;
};

type TableActionProps = {
  edit?: ActionOverrides;
  remove?: ActionOverrides;
  view?: ActionOverrides;
  renderCustomActions?: () => React.ReactNode;
};

const TableActions: React.FC<TableActionProps> = ({ edit, remove, view }) => {
  const COMMON_ACTIONS: ActionOverrides[] = [
    {
      ...view,
      Icon: view?.Icon || VisibilityOutlined,
      title: view?.title || "View",
      onClick: view?.onClick,
      tooltip: view?.tooltip || "View",
      disabled: view?.disabled,
      hide: view?.hide,
    },
    {
      ...edit,
      Icon: edit?.Icon || Edit,
      title: edit?.title || "Edit",
      onClick: edit?.onClick,
      tooltip: edit?.tooltip || "Edit",
      disabled: edit?.disabled,
      hide: edit?.hide,
    },
    {
      ...remove,
      Icon: remove?.Icon || Delete,
      title: remove?.title || "Remove",
      onClick: remove?.onClick,
      tooltip: remove?.tooltip || "Remove",
      disabled: remove?.disabled,
      hide: remove?.hide,
    },
  ];

  return (
    <Box>
      {COMMON_ACTIONS.filter((eachAction) => !eachAction?.hide).map(({ Icon, ...action }, index) => (
        <Tooltip title={action?.tooltip || ""} key={index}>
          <IconButton
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              if (!action?.onClick) {
                throw new Error("Action onClick is required");
              }
              action?.onClick;
            }}
            disabled={action?.disabled}
          >
            {Icon && <Icon color="primary" {...action} />}
          </IconButton>
        </Tooltip>
      ))}
    </Box>
  );
};

export default TableActions;
