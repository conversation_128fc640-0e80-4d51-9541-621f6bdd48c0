import { <PERSON>, Button, Tooltip, Typography } from "@mui/material";
import { useMutation, useQueries } from "@tanstack/react-query";
import { MRT_ColumnDef, MRT_RowSelectionState } from "material-react-table";
import React, { useCallback, useMemo, useState } from "react";

import { LeaveApprovalData } from "src/services/data_transformers/leave.transforms";
import LeaveServiceAPI from "src/services/leaves.service";
import validators from "src/utils/validators";

import { getStatusColors } from "src/utils/typographyUtils";
import { FormActions } from "../Common/CRUDTableV2";
import { EmployeeCellInfo } from "../Common/EmployeeViews/EmployeeCellInfo";
import Modal from "../Common/Modal/Modal";
import Span from "../Common/Span/Span";
import DataTable from "../Common/Table/DataTable";
import { CommonFormWithState } from "../Employees/components/CommonForm";
import { FormInputType } from "../Employees/types/FormDataTypes";

import { InfoOutlined } from "@mui/icons-material";
import { DD_MM_YYYY, formatDateToDayMonthYear, getIntlTimeToSpecifiedFormat } from "src/utils/dateUtils";
import RejectionConfirmationModal from "../EmployeeAttendance/components/components/RejectionConfirmationModal";

const BULK_LENGTH_DENOMINATOR = 1;

const typography = {
  approve: "Approve",
  reject: "Reject",
};

enum LeaveApprovalStatus {
  PENDING = "Pending",
  CANCELATION_REQUESTED = "Cancellation Requested",
}

const columns: MRT_ColumnDef<LeaveApprovalData>[] = [
  {
    accessorKey: "raisedBy.displayName",
    header: "Employee",
    Cell: ({ row }) => {
      return (
        <EmployeeCellInfo
          name={row?.original?.raisedBy?.displayName}
          jobTitle={row?.original?.raisedBy?.designation}
          displayPic={row?.original?.raisedBy?.display_pic}
        />
      );
    },
    size: 200,
  },
  {
    accessorKey: "startDate",
    header: "Start Date",
    size: 150,
    Cell: ({ row }) => formatDateToDayMonthYear(row?.original?.startDate),
  },
  {
    accessorKey: "endDate",
    header: "End Date",
    size: 150,
    Cell: ({ row }) => formatDateToDayMonthYear(row?.original?.endDate),
  },
  {
    accessorKey: "duration",
    header: "Duration",
    size: 120,
  },
  {
    accessorKey: "durationType",
    header: "Applied For",
    size: 150,
  },
  {
    accessorKey: "requestType",
    header: "Type",
    size: 200,
  },
  {
    accessorKey: "reason",
    header: "Reason",
    size: 200,
  },
  {
    accessorKey: "status",
    header: "Status",
    Cell: ({ row }) => (
      <Box display="flex" alignItems="center" gap={1}>
        <Typography color={getStatusColors(row?.original?.status)}>{row?.original?.status}</Typography>
        {row?.original?.comment && (
          <Tooltip title={row?.original?.comment}>
            <InfoOutlined color="action" />
          </Tooltip>
        )}
      </Box>
    ),
  },
  {
    accessorKey: "raisedOn",
    header: "Raised On",
    Cell: ({ row }) => getIntlTimeToSpecifiedFormat(row?.original?.raisedOn, DD_MM_YYYY).formattedDate,
  },
];

const rejectForm: FormInputType[] = [
  {
    name: "comment",
    label: "Comment",
    variant: "text",
    rows: 3,
    isRequired: true,
  },
];

const LeaveApproval = () => {
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showBulkApproveModal, setShowBulkApproveModal] = useState(false);
  const [showBulkRejectModal, setShowBulkRejectModal] = useState(false);
  const [currentRequest, setCurrentRequest] = useState<LeaveApprovalData | null>(null);
  const [rowSelection, setRowSelection] = useState<MRT_RowSelectionState>({});
  const areActionsButtonsEnabled = useMemo(() => Object.keys(rowSelection).length > 0, [rowSelection]);

  const result = useQueries({
    queries: [
      {
        queryKey: ["leave-request-approval"],
        queryFn: async (): Promise<LeaveApprovalData[]> => LeaveServiceAPI.getLeaveRequestToApprove(),
        retryOnMount: false,
        refetchOnWindowFocus: false,
      },
    ],
  });
  const [{ data: leaveApproval, isLoading: isLeaveApprovalLoading, refetch: refetchLeaveApproval }] = result;

  const selectedRows = useMemo((): LeaveApprovalData[] => {
    if (!leaveApproval || leaveApproval?.length === 0) return [];

    return Object.keys(rowSelection)
      .map(Number)
      .map((index) => leaveApproval[index])
      .filter(
        (row) =>
          row &&
          (row?.status === LeaveApprovalStatus.PENDING || row?.status === LeaveApprovalStatus.CANCELATION_REQUESTED),
      );
  }, [leaveApproval, rowSelection]);

  const selectedRequestIds = useMemo(() => {
    return selectedRows.map((row) => row?.requestId).filter(Boolean) as string[];
  }, [selectedRows]);

  const approveBatchRequests = useMutation({
    mutationKey: ["approve-batch-leave-requests"],
    mutationFn: async (requestIds: string[]) => LeaveServiceAPI.approveBatchLeaveRequests(requestIds),
    onSuccess: (success) => {
      if (success) {
        refetchLeaveApproval();
        setRowSelection({});
      }
    },
  });

  const rejectBatchRequests = useMutation({
    mutationKey: ["reject-batch-leave-requests"],
    mutationFn: async (payload: { request_ids: string[]; comment: string }) =>
      LeaveServiceAPI.rejectBatchLeaveRequests(payload),
    onSuccess: (success: boolean) => {
      if (success) {
        refetchLeaveApproval();
        setRowSelection({});
      }
    },
  });

  const onApproveClick = useCallback(() => {
    if (selectedRows?.length > BULK_LENGTH_DENOMINATOR) {
      setShowBulkApproveModal(true);
      return;
    }
    setCurrentRequest(selectedRows[0]);
    setShowApproveModal(true);
  }, [selectedRows]);

  const onRejectClick = () => {
    if (selectedRequestIds.length > BULK_LENGTH_DENOMINATOR) {
      setShowBulkRejectModal(true);
      return;
    }
    setCurrentRequest(selectedRows[0]);
    setShowRejectModal(true);
  };

  const handleBulkApprove = () => {
    approveBatchRequests.mutate(selectedRequestIds);
    setShowBulkApproveModal(false);
  };

  const handleBulkReject = (comment: string) => {
    const payload = {
      request_ids: selectedRequestIds,
      comment: comment,
    };
    rejectBatchRequests.mutate(payload);
    setShowBulkRejectModal(false);
  };

  const updateLeaveRequests = leaveApproval?.map((leaveRequest) => ({
    ...leaveRequest,
    raisedOn: leaveRequest.raisedOn,
    startDate: leaveRequest.startDate,
    endDate: leaveRequest.endDate,
  }));

  const rejectModelContent = currentRequest
    ? [
        {
          title: "name",
          value: currentRequest.raisedBy?.displayName,
        },
        {
          title: "Leave Type",
          value: currentRequest?.requestType,
        },
        {
          title: "Created On",
          value: getIntlTimeToSpecifiedFormat(currentRequest?.raisedOn, DD_MM_YYYY).formattedDate,
        },
        {
          title: "Applied Date",
          value: `${formatDateToDayMonthYear(currentRequest.startDate)} - ${formatDateToDayMonthYear(currentRequest.endDate)}`,
        },
        {
          title: "Duration",
          value: currentRequest.duration,
        },
        {
          title: "Applied For",
          value: currentRequest.durationType,
        },
        {
          title: "Description",
          value: currentRequest.reason,
        },
      ]
    : [];

  const handleNextClick = (formDetails: any) => {
    if (showApproveModal) {
      handleBulkApprove();
      setShowApproveModal(false);
      return;
    }
    handleBulkReject(formDetails.comment);
    setShowRejectModal(false);
  };

  return (
    <>
      <Box display="flex" justifyContent="flex-end" sx={{ marginBottom: "20px" }} gap={1}>
        <Button disabled={!areActionsButtonsEnabled} variant="contained" color="primary" onClick={onApproveClick}>
          {typography.approve}
        </Button>
        <Button disabled={!areActionsButtonsEnabled} variant="outlined" color="error" onClick={onRejectClick}>
          {typography.reject}
        </Button>
      </Box>
      {!isLeaveApprovalLoading && updateLeaveRequests && (
        <DataTable
          layoutMode="semantic"
          columns={columns}
          data={updateLeaveRequests}
          enableRowSelection={(row) =>
            row?.original?.status === LeaveApprovalStatus.PENDING ||
            row?.original?.status === LeaveApprovalStatus.CANCELATION_REQUESTED
          }
          onRowSelectionChange={setRowSelection}
          state={{
            rowSelection: rowSelection,
          }}
        />
      )}
      {(showApproveModal || showRejectModal) && (
        <Modal
          showBackButton
          subtitle="Approve or Reject Leave of your reportees"
          isOpen={showApproveModal || showRejectModal}
          title={showApproveModal ? "Approve Leave" : "Reject Leave"}
          onClose={() => {
            setShowApproveModal(false);
            setShowRejectModal(false);
          }}
          PaperProps={{ sx: { borderRadius: "20px" } }}
        >
          <Box sx={{ padding: "0 16px" }}>
            <Span>Details</Span>
            <Box sx={{ border: "1px solid #EDEDED", borderRadius: 4, marginTop: "10px" }}>
              {rejectModelContent.map((content, index) => (
                <Box
                  key={content.title}
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    borderTop: index === 0 ? "none" : "1px solid #EDEDED",
                    padding: "16px",
                  }}
                >
                  <Span>{content.title}</Span>
                  <Span>{content.value}</Span>
                </Box>
              ))}
            </Box>
            <Box sx={{ marginTop: "30px", width: "100%" }}>
              <CommonFormWithState
                initialState={showApproveModal ? {} : { comment: "" }}
                validators={showApproveModal ? {} : { comment: [validators.validateInput] }}
                inputElements={showApproveModal ? [] : rejectForm}
                gridStyles={{ rowSpacing: 4 }}
                onFormSubmit={handleNextClick}
                ActionComponent={(props) => (
                  <FormActions
                    hideCancelButton
                    onSubmitClick={props.onSubmit}
                    onCancelClick={() => {}}
                    submitButtonText={showApproveModal ? typography.approve : typography.reject}
                    disabled={!props.areFormDetailsValid}
                  />
                )}
              />
            </Box>
          </Box>
        </Modal>
      )}

      {showBulkApproveModal && (
        <Modal
          isOpen={showBulkApproveModal}
          title="Approve Leave Request(s)"
          onClose={() => setShowBulkApproveModal(false)}
          sx={{ borderRadius: "20px" }}
        >
          <Box padding={2}>
            <Typography marginBottom="20px">
              You are about to approve {selectedRequestIds.length} leave request
              {selectedRequestIds.length > 1 ? "s" : ""}. Do you want to proceed?
            </Typography>
            <Box display="flex" justifyContent="flex-end">
              <Button variant="contained" onClick={handleBulkApprove}>
                {typography?.approve}
              </Button>
            </Box>
          </Box>
        </Modal>
      )}

      {showBulkRejectModal && (
        <RejectionConfirmationModal
          onClose={() => setShowBulkRejectModal(false)}
          onSubmit={handleBulkReject}
          title="Reject Leave Request(s)"
        />
      )}
    </>
  );
};

export default LeaveApproval;
