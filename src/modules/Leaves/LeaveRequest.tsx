import { InfoOutlined } from "@mui/icons-material";
import { Box, Tooltip } from "@mui/material";
import { useMutation, useQueries, useQuery } from "@tanstack/react-query";
import { addDays, eachDayOfInterval, format, getDay, isSameDay, parseISO } from "date-fns";
import { MRT_ColumnDef } from "material-react-table";
import React, { useEffect, useMemo, useState } from "react";

import { ApplyLeaveRequest, EditLeaveRequestType } from "src/services/api_definitions/leave.service";
import { LeaveRequestData, LeaveSummaryData } from "src/services/data_transformers/leave.transforms";
import LeaveServiceAPI from "src/services/leaves.service";
import validators from "src/utils/validators";

import { useLocation, useNavigate } from "react-router-dom";
import { useForm } from "src/customHooks/useForm";
import dashboardService from "src/services/dashboard.service";
import departmentService from "src/services/department.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";
import Span from "../Common/Span/Span";
import { PATH_CONFIG } from "../Routing/config";
import { CrudTable } from "../Settings/components/Common/CrudTable";

type Props = {
  leaveSummary: LeaveSummaryData[];
  refetchLeaveSummary: () => void;
  isLoading: boolean;
};

const tableColumns: MRT_ColumnDef<LeaveRequestData>[] = [
  {
    accessorKey: "requestType",
    header: "Type",
  },
  {
    accessorKey: "startDate",
    header: "Start Date",
    Cell: ({ row }) => formatDateToDayMonthYear(row.original.startDate),
  },
  {
    accessorKey: "endDate",
    header: "End Date",
    Cell: ({ row }) => formatDateToDayMonthYear(row.original.endDate),
  },
  {
    accessorKey: "duration",
    header: "Duration",
  },
  {
    accessorKey: "durationType",
    header: "Applied For",
  },
  {
    accessorKey: "reason",
    header: "Reason",
  },
  {
    accessorKey: "approver",
    header: "Approver",
  },
  {
    accessorKey: "raisedOn",
    header: "Raised On",
    Cell: ({ row }) => formatDateToDayMonthYear(row.original.raisedOn),
  },
  {
    accessorKey: "status",
    header: "Status",
    Cell: ({ row }) => (
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <Span color={getStatusColors(row?.original?.status)} sx={{ display: "flex", alignItems: "center" }}>
          {row.original.status}
        </Span>
        {row.original.status === "Rejected" && (
          <Tooltip placement="top" title={row.original.comment}>
            <InfoOutlined fontSize="small" sx={{ cursor: "pointer", marginLeft: "8px", width: 16, height: 16 }} />
          </Tooltip>
        )}
      </Box>
    ),
  },
];

const dayNameToIndex = {
  SUNDAY: 0,
  MONDAY: 1,
  TUESDAY: 2,
  WEDNESDAY: 3,
  THURSDAY: 4,
  FRIDAY: 5,
  SATURDAY: 6,
};

const DATE_DROPDOWN_ENTITIES = [
  {
    value: "Full Day",
    label: "Full Day",
  },
  {
    value: "First Half",
    label: "First Half",
  },
  {
    value: "Second Half",
    label: "Second Half",
  },
];

const durationType = (selectedRequestType: string) => {
  switch (selectedRequestType) {
    case "Public Holiday / Optional Leave":
      return DATE_DROPDOWN_ENTITIES.filter((entity) => entity.value === "Full Day");
    default:
      return [...DATE_DROPDOWN_ENTITIES];
  }
};

const inputElements = [
  {
    name: "requestType",
    label: "Request Type",
    variant: "select",
    xs: 12,
    placeholder: "Select Request Type",
    isEditable: true,
    isRequired: true,
  },
  {
    name: "durationType",
    label: "Duration",
    variant: "select",
    xs: 6,
    placeholder: "Select Duration",
    isEditable: true,
    isRequired: true,
  },
  {
    name: "appliedDateRange",
    label: "Applied Date",
    variant: "date-range",
    xs: 6,
    placeholder: "Select Applied Date",
    isEditable: true,
    isRequired: true,
  },
  {
    name: "reason",
    label: "Reason",
    variant: "text",
    xs: 12,
    isEditable: true,
    isRequired: true,
    rows: 3,
  },
];

const formValidators = {
  requestType: [validators.validateInput],
  duration: [validators.validateInput],
  appliedDateRange: [validators.validateInput],
  reason: [validators.validateInput],
  durationType: [validators.validateInput],
};

const getActionConfig = (status: string, startDate: string) => {
  const today = new Date();
  const leaveStartDate = new Date(startDate);

  switch (status) {
    case "Pending":
      return {
        hideEdit: false,
        hideDelete: false,
      };
    case "Approved":
      return {
        hideEdit: leaveStartDate < today,
        hideDelete: false,
      };
    case "Rejected":
      return {
        hideEdit: true,
        hideDelete: false,
      };
    case "Canceled":
      return {
        hideEdit: true,
        hideDelete: false,
      };
    case "Cancellation Requested":
      return {
        hideEdit: true,
        hideDelete: true,
      };
    default:
      return {
        hideEdit: false,
        hideDelete: false,
      };
  }
};

const rowAdditionaInitialValues = {
  requestType: "",
  appliedDateRange: {
    startDate: format(new Date(), "yyyy-MM-dd"),
    endDate: format(new Date(), "yyyy-MM-dd"),
  },
  durationType: "",
  reason: "",
};

const LeaveRequest = ({ leaveSummary, refetchLeaveSummary, isLoading }: Props) => {
  const selectedYear = new Date().getFullYear();
  const minDate = addDays(new Date(), -90);
  const maxDate = addDays(new Date(), 366);
  const queryParams = new URLSearchParams(location.search);
  const navigate = useNavigate();
  const { search } = useLocation();
  const [selectedRequestType, setSelectedRequestType] = useState("");

  const isAddModalOpen = () => {
    if (queryParams.has("action")) {
      const openModal = queryParams.get("action") === "apply";
      queryParams.delete("action");
      navigate(PATH_CONFIG.LEAVES.path, {
        replace: true,
        state: {
          search: queryParams.toString(),
        },
      });
      return openModal;
    }
    return false;
  };

  const { data: organisationHolidays = { nonMandatoryHolidays: [], mandatoryHolidays: [], nextHoliday: new Date() } } =
    useQuery(
      ["get-org-holidays", selectedYear],
      async () => {
        const currentYearResp = await dashboardService.getHolidays(selectedYear.toString());
        const nextYearResponse = await dashboardService.getHolidays((selectedYear + 1).toString());
        const resp = [...(currentYearResp || []), ...(nextYearResponse || [])];
        return {
          nonMandatoryHolidays: resp
            ?.filter((item) => !item.is_mandatory)
            ?.map((dateString) => ({ date: parseISO(dateString.date), name: dateString.name })),
          mandatoryHolidays: resp
            ?.filter((item) => item.is_mandatory)
            ?.map((dateString) => ({ date: parseISO(dateString.date), name: dateString.name })),
          response: resp,
          nextHoliday: resp
            ?.filter((item) => !item.is_mandatory)
            ?.map((dateString) => parseISO(dateString.date))
            ?.find((date) => date > new Date()),
        };
      },
      {
        retryOnMount: false,
        refetchInterval: false,
        refetchOnWindowFocus: false,
      },
    );

  const getAppliedDateRange = (appliedDateRange: { startDate: string; endDate: string }) => {
    switch (selectedRequestType) {
      case "Public Holiday / Optional Leave":
        return {
          startDate: format(organisationHolidays?.nextHoliday || new Date(), "yyyy-MM-dd"),
          endDate: format(organisationHolidays?.nextHoliday || new Date(), "yyyy-MM-dd"),
        };
      default:
        return appliedDateRange;
    }
  };

  useEffect(() => {
    useFormDetails?.setFormDetail(
      "appliedDateRange",
      getAppliedDateRange(useFormDetails?.formDetails?.appliedDateRange),
    );
    if (selectedRequestType === "Public Holiday / Optional Leave") {
      useFormDetails?.setFormDetail("durationType", "Full Day");
    }
  }, [selectedRequestType]);

  const { data: attendanceConfigDetails } = useQuery(
    ["get-attendance-config"],
    async () => departmentService.getAttendanceConfig(),
    {
      retryOnMount: false,
      refetchInterval: false,
      refetchOnWindowFocus: false,
    },
  );

  const getNonOptionalDates = (enabledDates: { date: Date; name: string }[], startDate: Date, endDate: Date) => {
    const allDates = eachDayOfInterval({ start: startDate, end: endDate });

    const weekOffDays = (attendanceConfigDetails?.week_offs as string[])?.map(
      (day: string) => dayNameToIndex[day as keyof typeof dayNameToIndex],
    );

    const disabledDates = allDates.filter((date) => {
      const dayOfWeek = getDay(date);
      return (
        !enabledDates.some((enabledDate) => isSameDay(enabledDate?.date, date)) || weekOffDays?.includes(dayOfWeek)
      );
    });

    return disabledDates;
  };

  const customDayContent = (day: Date) => {
    const isWeeklyOff = (attendanceConfigDetails?.week_offs as string[])?.includes(format(day, "EEEE").toUpperCase());
    const matchedHoliday = organisationHolidays?.mandatoryHolidays?.find((holiday) => isSameDay(holiday?.date, day));

    const isHoliday = !!matchedHoliday;
    const holidayName = matchedHoliday?.name;

    const toolTipTitle = isWeeklyOff ? "Week Off" : isHoliday ? `Mandatory Holiday: ${holidayName}` : "";
    const isMandatoyLeave = isWeeklyOff || isHoliday;

    const customStyle = {
      backgroundColor: "rgb(248, 248, 248)",
      width: "100%",
      borderRadius: "16px",
    };
    if (selectedRequestType === "Public Holiday / Optional Leave") {
      const currentDay = organisationHolidays?.nonMandatoryHolidays?.find((holiday) => isSameDay(holiday?.date, day));

      const isNonMandatoryHoliday = !!currentDay;
      const nonMandatoryHolidayName = currentDay?.name;

      return (
        <Tooltip title={isNonMandatoryHoliday ? nonMandatoryHolidayName : ""} arrow>
          <div>
            <span>{format(day, "d")}</span>
          </div>
        </Tooltip>
      );
    }
    return (
      <Tooltip title={toolTipTitle} arrow>
        <div style={isMandatoyLeave ? customStyle : {}}>
          <span style={{ color: isMandatoyLeave ? "#1D2428" : "" }}>{format(day, "d")}</span>
        </div>
      </Tooltip>
    );
  };

  const result = useQueries({
    queries: [
      {
        queryKey: ["get-leave-requests"],
        queryFn: async (): Promise<LeaveRequestData[]> => LeaveServiceAPI.getLeaveRequests(),
        retryOnMount: false,
        refetchOnWindowFocus: false,
      },
    ],
  });
  const [{ data: leaveRequests, isLoading: isLeaveRequestLoading, refetch: refetchLeaveRequests }] = result;
  const updateLeaveRequests = leaveRequests?.map((leaveRequest) => ({
    ...leaveRequest,
    ...getActionConfig(leaveRequest.status, leaveRequest.startDate),
    appliedDateRange: {
      startDate: leaveRequest.startDate,
      endDate: leaveRequest.endDate,
    },
  }));

  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const selectedRowData = useMemo(
    () => (selectedRow !== null ? updateLeaveRequests?.[selectedRow] : rowAdditionaInitialValues),
    [selectedRow],
  );

  const useFormDetails = useForm<any>({
    initialState: selectedRowData,
    isBulk: false,
    validations: formValidators,
  });

  useEffect(() => {
    setSelectedRequestType(useFormDetails?.formDetails?.requestType);
  }, [useFormDetails?.formDetails?.requestType]);

  const applyLeave = useMutation({
    mutationKey: ["apply-leave"],
    mutationFn: async (payload: ApplyLeaveRequest) => LeaveServiceAPI.applyLeave(payload),
    onSuccess: (success) => {
      if (success) {
        refetchLeaveSummary();
        refetchLeaveRequests();
      }
    },
  });

  const editLeave = useMutation({
    mutationKey: ["edit-leave"],
    mutationFn: async (payload: EditLeaveRequestType) => LeaveServiceAPI.editLeaveRequest(payload),
    onSuccess: (sucess) => {
      if (sucess) {
        refetchLeaveSummary();
        refetchLeaveRequests();
      }
    },
  });

  const deleteLeaveRequest = useMutation({
    mutationKey: ["delete-leave"],
    mutationFn: async (requestId: string) => LeaveServiceAPI.deleteLeaveRequest(requestId),
    onSuccess: (success) => {
      if (success) {
        refetchLeaveSummary();
        refetchLeaveRequests();
      }
    },
  });

  const disabledDates = (selectedRequestType: string) => {
    switch (selectedRequestType) {
      case "Public Holiday / Optional Leave":
        return getNonOptionalDates(organisationHolidays?.nonMandatoryHolidays, minDate, maxDate);

      default:
        return [];
    }
  };

  const onEditClick = async (leaveRequestsData: typeof rowAdditionaInitialValues, leaveIndex: number) => {
    const requestId = leaveRequests?.[leaveIndex].requestId;
    if (requestId && leaveRequestsData) {
      const { requestType, durationType, appliedDateRange, reason } = leaveRequestsData;
      await editLeave.mutateAsync({
        request_id: requestId,
        leave_type: requestType,
        duration_type: durationType,
        reason,
        start_date: appliedDateRange.startDate,
        end_date: appliedDateRange.endDate,
      });
    }
  };

  const onAddClick = async (leaveRequestsData: typeof rowAdditionaInitialValues) => {
    if (leaveRequestsData) {
      const { requestType, durationType, appliedDateRange, reason } = leaveRequestsData;
      await applyLeave.mutateAsync({
        leave_type: requestType,
        duration_type: durationType,
        reason,
        start_date: appliedDateRange.startDate,
        end_date: appliedDateRange.endDate,
      });
    }
  };

  const onDeleteConfirmed = (leaveIndex: number) => {
    const requestId = leaveRequests?.[leaveIndex].requestId;
    if (requestId) {
      deleteLeaveRequest.mutate(requestId);
    }
  };

  const editFormConfig = {
    nextButtonText: "Save",
    onNextClick: onEditClick,
    formTitle: "Modify Leave",
    customDayContent: customDayContent,
  };

  const addFormConfig = {
    onNextClick: onAddClick,
    formTitle: "Apply leave",
    addButtonText: "Apply leave",
    nextButtonText: "Apply leave",
    customDayContent: customDayContent,
  };

  const deleteFormConfig = {
    nextButtonText: "Delete",
    formTitle: "Delete Leave",
    onNextClick: onDeleteConfirmed,
    getQuestion: () => "Are you sure you want to delete this Leave request ?",
  };

  const onRangeFocusChange =
    selectedRequestType === "Public Holiday / Optional Leave" ||
    useFormDetails?.formDetails?.durationType === "First Half" ||
    useFormDetails?.formDetails?.durationType === "Second Half"
      ? () => {}
      : undefined;

  const disableDragSelection =
    selectedRequestType === "Public Holiday / Optional Leave" ||
    useFormDetails?.formDetails?.durationType === "First Half" ||
    useFormDetails?.formDetails?.durationType === "Second Half";

  const selectOptions = {
    requestType:
      leaveSummary
        ?.filter((leave) => leave?.noOfLeaves > 0)
        ?.map((summary) => ({
          value: summary.leaveType,
          label: summary.leaveType,
        })) || [],
    durationType: durationType(selectedRequestType),
    appliedDateRange: {
      minDate,
      maxDate,
      disabledDates: disabledDates(selectedRequestType),
      onFocusChange: onRangeFocusChange,
      dragSelectionEnabled: !disableDragSelection,
    },
  };

  const readOnlyFields = {
    requestType: useFormDetails?.formDetails?.status !== "Pending",
  };

  return (
    <CrudTable
      formConfig={{
        addFormConfig: addFormConfig,
        editFormConfig: editFormConfig,
        deleteFormConfig: deleteFormConfig,
      }}
      selectOptions={selectOptions}
      columns={tableColumns}
      enableRowNumbers={false}
      inputElements={inputElements}
      formValidators={formValidators}
      isLoading={isLeaveRequestLoading || isLoading}
      defaultFormState={updateLeaveRequests}
      rowAdditionaInitialValues={rowAdditionaInitialValues}
      defaultOpenAddModal={isAddModalOpen(search)}
      useFormDetails={useFormDetails}
      setSelectedRow={setSelectedRow}
      selectedRow={selectedRow}
      readOnlyFields={readOnlyFields}
    />
  );
};

export default LeaveRequest;
