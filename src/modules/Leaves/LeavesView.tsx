import React, { useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { LeaveSummaryData } from "src/services/data_transformers/leave.transforms";
import LeaveRequest from "./LeaveRequest";
import ManagerLeaveView from "./ManagerLeaveView";

interface Props {
  isLoading: boolean;
  leaveSummary: LeaveSummaryData[];
  refetch: () => void;
}

const LeavesView: React.FC<Props> = ({ isLoading, leaveSummary, refetch }) => {
  const {
    userDetails: { is_manager: isManager },
  } = useAppSelector((state) => state.userManagement);
  const commonProps = useMemo(() => {
    return {
      isLoading,
      leaveSummary,
      refetchLeaveSummary: refetch,
    };
  }, [isLoading, leaveSummary]);

  if (isManager) {
    return <ManagerLeaveView {...commonProps} />;
  }
  return <LeaveRequest {...commonProps} />;
};
export default LeavesView;
