import { Avatar, Box, CircularProgress, Grid, <PERSON>u, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/material";
import React, { lazy, Suspense, useEffect } from "react";
import SearchBar from "./searchBar/SearchBar";

import { useQuery } from "@tanstack/react-query";

import { useQueries } from "@tanstack/react-query";
import { useAppSelector } from "src/customHooks/useAppSelector";
import NotificationFirebase from "src/firebase";
import notificationService from "src/services/notification.service";
import { menuButtonConfig } from "../navigation.config";
import {
  AppBar,
  CustomGrid,
  IconButton,
  IconContainer,
  IconStyles,
  MenuStyles,
  ToolbarStyles,
} from "./MenuTopBar.styles";

interface MenuBarProps {
  open: boolean;
  isHidden?: boolean;
}
enum MenuItems {
  AVATAR = "avatar",
  ADD = "add",
  NOTIFICATION = "notification",
  INFO = "info",
}

const LazyAvatarComponent = lazy(() => import("./appbarMenus/AvatarMenu"));
const LazyAddMenu = lazy(() => import("./appbarMenus/AddMenu"));
const LazyInfoMenu = lazy(() => import("./appbarMenus/InfoMenu"));
const LazyNotificationMenu = lazy(() => import("./appbarMenus/NotificationMenu"));

const MenuTopBar: React.FC<MenuBarProps> = ({ open, isHidden }) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [selectedMenu, setSelectedMenu] = React.useState<string | null>(null);
  const [notificationCount, setNotificationCount] = React.useState<number>(0);
  const { userDetails, userRoles } = useAppSelector((state) => state.userManagement);

  const result = useQueries({
    queries: [
      {
        queryKey: ["get-unread-notifications-count"],
        queryFn: async (): Promise<number> => notificationService.getUnreadNotificationCount(),
        retryOnMount: false,
        refetchOnWindowFocus: false,
        enabled: userRoles?.includes("Employee") && userDetails?.organisations?.length > 0,
      },
    ],
  });
  const {
    data: unreadNotificationCount,
    refetch: refetchNotificationsCount,
    isLoading: isUnreadNotificationLoading,
  } = result[0];

  const { data: employeeDetails } = useQuery({
    queryKey: ["employee-details"],
    enabled: false,
  });

  const avatarSrc = React.useMemo(() => {
    return userDetails?.display_pic || "";
  }, [userDetails, employeeDetails]);

  useEffect(() => {
    if (!isUnreadNotificationLoading && unreadNotificationCount != null) {
      setNotificationCount(unreadNotificationCount);
    }
  }, [unreadNotificationCount, isUnreadNotificationLoading]);

  const handleIconClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    setSelectedMenu(event.currentTarget.id);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSelectedMenu(null);
  };

  const getMenuComponent = React.useMemo(() => {
    switch (selectedMenu) {
      case MenuItems.AVATAR:
        return <LazyAvatarComponent handleMenuClose={handleClose} avatarSrc={avatarSrc} />;
      case MenuItems.ADD:
        return <LazyAddMenu handleMenuClose={handleClose} />;
      case MenuItems.NOTIFICATION:
        return <LazyNotificationMenu onNotificationRead={refetchNotificationsCount} />;
      case MenuItems.INFO:
        return <LazyInfoMenu />;
      default:
        return null;
    }
  }, [selectedMenu]);

  if (isHidden) {
    return null;
  }

  return (
    <AppBar position="fixed" elevation={0} open={open}>
      <NotificationFirebase onNewNotificationReceived={refetchNotificationsCount} />
      <Toolbar sx={ToolbarStyles.root}>
        <Box sx={ToolbarStyles.actions}>
          <SearchBar />
          <Grid container spacing={2} sx={{ width: "max-content", padding: "0 1rem", alignItems: "center" }}>
            {menuButtonConfig.map((menuConfig) => {
              return (
                <CustomGrid item key={menuConfig.title}>
                  <IconContainer>
                    <Tooltip title={menuConfig.title}>
                      <>
                        <IconButton id={menuConfig.id} onClick={handleIconClick} style={IconStyles.root}>
                          <menuConfig.icon style={menuConfig.id === "add" ? IconStyles.primaryIcon : IconStyles.icon} />
                        </IconButton>
                        {menuConfig.id === "notification" && notificationCount > 0 && (
                          <Box sx={IconStyles.badge}>{notificationCount}</Box>
                        )}
                      </>
                    </Tooltip>
                  </IconContainer>
                </CustomGrid>
              );
            })}
            <Grid item>
              <IconContainer>
                <Tooltip title="Open settings">
                  <IconButton id="avatar" onClick={handleIconClick} sx={IconStyles.root}>
                    <Avatar alt={userDetails?.display_name} sx={IconStyles.root} src={avatarSrc} />
                  </IconButton>
                </Tooltip>
              </IconContainer>
            </Grid>
          </Grid>
        </Box>
        <Menu
          id="menu-settings"
          anchorEl={anchorEl}
          keepMounted
          open={Boolean(anchorEl)}
          onClose={handleClose}
          transformOrigin={{ horizontal: "right", vertical: "top" }}
          anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
          sx={MenuStyles.root}
        >
          <Suspense fallback={<CircularProgress />}>{getMenuComponent}</Suspense>
        </Menu>
      </Toolbar>
    </AppBar>
  );
};

export default MenuTopBar;
