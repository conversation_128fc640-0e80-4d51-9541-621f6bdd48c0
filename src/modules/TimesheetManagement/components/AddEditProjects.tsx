import { Add, Delete } from "@mui/icons-material";
import { Box, Button, Grid2, IconButton, InputLabel, Stack, Switch, Typography } from "@mui/material";
import { TimeField } from "@mui/x-date-pickers";
import { parse } from "date-fns";
import React from "react";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import CustomSearchField from "src/modules/Common/FormInputs/CustomSearchField";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";

interface Props {
  form: any;
  isEdit: boolean;
}

const billingTypeOptions = [
  { value: "", label: "Select" },
  { value: "Project Level", label: "Project Level" },
  { value: "Resource Level", label: "Resource Level" },
];

const currencyOptions = [
  { value: "", label: "Select" },
  { value: "USD", label: "USD" },
  { value: "EUR", label: "EUR" },
  { value: "GBP", label: "GBP" },
  { value: "INR", label: "INR" },
];

const AddEditProjects: React.FC<Props> = ({ form }) => {
  // const employeeReporteeOptions =
  //   employeeData?.map((item: any) => ({
  //     name: `${item.employee_name} (${item.employee_code})`,
  //     displayPic: item.originalData.display_pic,
  //     jobTitle: item.job_title,
  //     code: item.employee_code,
  //   })) || [];

  return (
    <form.Subscribe
      selector={(state: any) => ({
        billable: state.values.billable || false,
        billingType: state.values.billing_type || "",
        startDate: state.values.start_date,
        internal: state.values.internal || false,
      })}
    >
      {(formState: { billable: boolean; billingType: string; startDate: Date; internal: boolean }) => {
        const showBillingFields = formState.billable;
        const isProjectLevel = formState.billingType === "Project Level";
        const isResourceLevel = formState.billingType === "Resource Level";
        console.log("formState", formState);
        return (
          <Grid2 container spacing={2}>
            <Grid2 size={6}>
              <form.Field name="project_code">
                {(field: any) => (
                  <CustomTextField
                    required
                    size="small"
                    fullWidth
                    title="Project Code"
                    name={field.name}
                    value={field.state.value as string}
                    onChange={(e) => field.handleChange(e.target.value)}
                    error={field.state?.meta?.errors?.length > 0}
                    helperText={field.state?.meta?.errors?.map((err) => err.message)}
                  />
                )}
              </form.Field>
            </Grid2>
            <Grid2 size={6}>
              <form.Field name="project_name">
                {(field: any) => (
                  <CustomTextField
                    required
                    size="small"
                    fullWidth
                    title="Project Name"
                    name={field.name}
                    value={field.state.value as string}
                    onChange={(e) => field.handleChange(e.target.value)}
                    error={field.state?.meta?.errors?.length > 0}
                    helperText={field.state?.meta?.errors?.map((err) => err.message)}
                  />
                )}
              </form.Field>
            </Grid2>
            <Grid2 size={6}>
              <form.Field name="start_date">
                {(field: any) => (
                  <CustomDateField
                    required={!formState.internal}
                    onChange={field.handleChange}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        size: "small",
                        // required: !formState.internal,
                        error: field.state?.meta?.errors?.length > 0,
                        helperText: field.state?.meta?.errors?.map((err) => err.message),
                      },
                    }}
                    value={field.state.value}
                    title="Start Date"
                    name={field.name}
                  />
                )}
              </form.Field>
            </Grid2>
            <Grid2 size={6}>
              <form.Field name="end_date">
                {(field: any) => (
                  <CustomDateField
                    required={!formState.internal}
                    onChange={field.handleChange}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        size: "small",
                        required: !formState.internal,
                      },
                    }}
                    value={field.state.value}
                    title="End Date"
                    name={field.name}
                    minDate={formState.startDate}
                  />
                )}
              </form.Field>
            </Grid2>
            {!formState.internal && (
              <Grid2 size={12}>
                <form.Field name="billable">
                  {(field: any) => (
                    <Box display="flex" alignItems="center" height="42px">
                      <InputLabel sx={{ fontSize: "14px" }} htmlFor="billable">
                        Billable
                      </InputLabel>
                      <Switch
                        checked={(field.state.value as boolean) || false}
                        id="billable"
                        name="billable"
                        value={(field.state.value as boolean) || false}
                        onChange={(ev) => field.handleChange(ev.target.checked)}
                      />
                    </Box>
                  )}
                </form.Field>
              </Grid2>
            )}
            {showBillingFields && (
              <Grid2 size={12}>
                <form.Field name="billing_type">
                  {(field: any) => (
                    <CustomSelect
                      size="small"
                      sx={{ width: "100%" }}
                      id="billing_type"
                      name="billing_type"
                      label="Billing Type"
                      required
                      options={billingTypeOptions}
                      value={(field.state.value as string) || ""}
                      error={field.state?.meta?.errors?.length > 0}
                      helperText={field.state?.meta?.errors?.map((err) => err.message)}
                      onChange={(event) => {
                        return field.handleChange(event.target.value);
                      }}
                    />
                  )}
                </form.Field>
              </Grid2>
            )}

            {showBillingFields && isProjectLevel && (
              <>
                <Grid2 size={6}>
                  <form.Field name="hourly_rate">
                    {(field: any) => (
                      <CustomTextField
                        required
                        size="small"
                        fullWidth
                        title="Hourly Rate"
                        name={field.name}
                        type="number"
                        value={field.state.value as string}
                        onChange={(e) => field.handleChange(Number(e.target.value))}
                        error={field.state?.meta?.errors?.length > 0}
                        helperText={field.state?.meta?.errors?.map((err) => err.message)}
                      />
                    )}
                  </form.Field>
                </Grid2>
                <Grid2 size={6}>
                  <form.Field name="currency_code">
                    {(field: any) => (
                      <CustomSelect
                        size="small"
                        sx={{ width: "100%" }}
                        id={field.name}
                        name={field.name}
                        label="Currency"
                        required
                        options={currencyOptions}
                        value={(field.state.value as string) || ""}
                        onChange={(event) => field.handleChange(event.target.value)}
                        error={field.state?.meta?.errors?.length > 0}
                        helperText={field.state?.meta?.errors?.map((err) => err.message)}
                      />
                    )}
                  </form.Field>
                </Grid2>
              </>
            )}

            {showBillingFields && isResourceLevel && (
              <>
                <Grid2 size={12}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle1">Resources</Typography>
                    <Typography variant="caption">Add resources to this project</Typography>
                  </Box>

                  <form.Field name="assignees" mode="array">
                    {(field: any) => {
                      const resources = field.state.value;

                      // Function to add a new resource
                      const addResource = () => {
                        field.pushValue({
                          employee_search_code: "",
                          employee_code: "",
                          employee_name: "",
                          hourly_rate: 0,
                          currency_code: "",
                          min_daily_hours_committed: undefined,
                          assigned: true,
                        });
                        // field.handleChange(newResources);
                      };

                      return (
                        <Stack spacing={3}>
                          {resources.map((_, index) => (
                            <Box
                              key={index}
                              sx={(theme) => {
                                const isAssigned = resources[index]?.assigned !== false;
                                return {
                                  p: 2,
                                  border: "1px solid #e0e0e0",
                                  borderRadius: 1,
                                  position: "relative",
                                  ...(isAssigned
                                    ? {}
                                    : {
                                        opacity: 0.7,
                                        backgroundColor: theme.palette.action.disabledBackground,
                                      }),
                                };
                              }}
                            >
                              <form.Field name={`assignees[${index}].assigned`}>
                                {(assignedField: any) => (
                                  <Box
                                    sx={{
                                      position: "absolute",
                                      top: 8,
                                      right: 8,
                                      display: "flex",
                                      alignItems: "center",
                                    }}
                                  >
                                    <InputLabel sx={{ fontSize: "14px", marginRight: "8px" }}>Assigned</InputLabel>
                                    <Switch
                                      size="small"
                                      checked={(assignedField.state.value as boolean) || false}
                                      onChange={(e) => assignedField.handleChange(e.target.checked)}
                                    />
                                  </Box>
                                )}
                              </form.Field>

                              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                                Resource {index + 1}
                              </Typography>

                              <Grid2 container spacing={2}>
                                <Grid2 size={12}>
                                  <form.Field name={`assignees[${index}].employee_code`}>
                                    {(resourceField: any) => {
                                      return (
                                        <CustomSearchField
                                          // options={(employeeReporteeOptions as any) || []}
                                          size="small"
                                          width="100%"
                                          getData="email"
                                          searchField="code"
                                          id={`assignees.${index}.employee_search_code`}
                                          title="Resource Name"
                                          required
                                          searchInputValue={(resourceField.state.value as string) || ""}
                                          placeholder="Search employee"
                                          isEmployeeView={true}
                                          error={resourceField.state?.meta?.errors?.length > 0}
                                          helperText={resourceField.state?.meta?.errors?.map((err) => err.message)}
                                          setSearchInputValue={(searchResult: string, originalRow: any) => {
                                            form.setFieldValue(
                                              `assignees[${index}].employee_search_code`,
                                              originalRow?.code,
                                            );
                                            return resourceField.handleChange(searchResult);
                                          }}
                                        />
                                      );
                                    }}
                                  </form.Field>
                                </Grid2>
                                <Grid2 size={4}>
                                  <form.Field name={`assignees[${index}].hourly_rate`}>
                                    {(resourceField: any) => (
                                      <CustomTextField
                                        required
                                        size="small"
                                        fullWidth
                                        title="Hourly Rate"
                                        name={resourceField.name}
                                        type="number"
                                        value={resourceField.state.value as string}
                                        onChange={(e) => resourceField.handleChange(Number(e.target.value))}
                                        error={resourceField.state?.meta?.errors?.length > 0}
                                        helperText={resourceField.state?.meta?.errors?.map((err) => err.message)}
                                      />
                                    )}
                                  </form.Field>
                                </Grid2>
                                <Grid2 size={4}>
                                  <form.Field name={`assignees[${index}].currency_code`}>
                                    {(resourceField: any) => (
                                      <CustomSelect
                                        size="small"
                                        sx={{ width: "100%" }}
                                        id={resourceField.name}
                                        name={resourceField.name}
                                        label="Currency"
                                        required
                                        options={currencyOptions}
                                        value={(resourceField.state.value as string) || ""}
                                        onChange={(event) => resourceField.handleChange(event.target.value)}
                                        error={resourceField.state?.meta?.errors?.length > 0}
                                        helperText={resourceField.state?.meta?.errors?.map((err) => err.message)}
                                      />
                                    )}
                                  </form.Field>
                                </Grid2>
                                <Grid2 size={4}>
                                  <form.Field name={`assignees[${index}].min_daily_hours_committed`}>
                                    {(resourceField: any) => (
                                      <>
                                        <CustomInputLabel title="Min Daily Hours Committed" />
                                        <TimeField
                                          // label="Min Hours Committed"
                                          value={resourceField.state.value}
                                          onChange={(e) => resourceField.handleChange(e)}
                                          slotProps={{
                                            textField: {
                                              fullWidth: true,
                                              size: "small",
                                              required: true,
                                              error: resourceField.state?.meta?.errors?.length > 0,
                                              helperText: resourceField.state?.meta?.errors?.map((err) => err.message),
                                            },
                                          }}
                                          name={resourceField.name}
                                          format="HH:mm"
                                          defaultValue={parse("00:00", "HH:mm", new Date())}
                                        />
                                      </>
                                    )}
                                  </form.Field>
                                </Grid2>
                              </Grid2>
                            </Box>
                          ))}

                          <Button
                            variant="outlined"
                            startIcon={<Add />}
                            onClick={addResource}
                            sx={{ alignSelf: "flex-start" }}
                          >
                            Add Resource
                          </Button>
                        </Stack>
                      );
                    }}
                  </form.Field>
                </Grid2>
              </>
            )}
          </Grid2>
        );
      }}
    </form.Subscribe>
  );
};

export default AddEditProjects;
