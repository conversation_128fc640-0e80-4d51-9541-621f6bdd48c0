import { Box } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useForm } from "src/customHooks/useForm";
import { useMasterData } from "src/customHooks/useMasterData";
import profileService from "src/services/profile.service";
import projectTrackingService from "src/services/projectTracking.service";
import validators from "src/utils/validators";
import { FormActions, ModalFormWrapper } from "../Common/CRUDTableV2";
import { CustomAutocomplete } from "../Common/FormInputs/CustomAutoComplete";
import { FormInputType } from "../Employees/types/FormDataTypes";
import TaskTableMRT from "./TaskTable";
import { inputElements } from "./config";

function extractCode(str: string) {
  const match = str.match(/\(([^)]+)\)/);
  return match ? match[1] : undefined;
}

type TaskOperationTypes = "add" | "edit" | "duplicate";
interface TaskRow {
  id: number | null;
  parentId?: number;
  taskType: "Project" | "Task" | "SubTask" | null;
  name: string;
  dueDate: string;
  priority: "High" | "Medium" | "Low" | null;
  estimatedHours: number | null;
  clientCode?: string;
  projectId?: string;
  taskId?: string;
  taskName?: string;
}

const defaultInputValues: TaskRow = {
  id: null,
  name: "",
  taskType: null,
  dueDate: "",
  priority: null,
  estimatedHours: null,
  clientCode: "",
};

const formValidators = {
  id: [],
  name: [validators.validateInput],
  taskType: [validators.validateInput],
  dueDate: [validators.validateInput],
  priority: [validators.validateInput],
  clientCode: [],
  projectId: [],
  taskId: [],
  estimatedHours: [],
};

const getChildDetails = (row: TaskRow) => {
  if (row.taskType === "Project") {
    return {
      taskType: "Task",
      parentId: row.id,
      projectId: row.id,
    };
  }
  if (row.taskType === "Task") {
    return {
      taskType: "SubTask",
      parentId: row.id,
      projectId: row.parentId,
      taskId: row.id,
    };
  }
  if (row.taskType === "SubTask") {
    return {
      taskType: "SubTask",
      parentId: row.id,
      projectId: row.projectId,
      taskId: row.id,
      taskName: row.name,
    };
  }
  return {};
};

const getParentName = (id: number, tasks: any) => {
  const parentNode = tasks.find((task: any) => task.id === id);
  if (parentNode?.taskType === "SubTask") {
    return parentNode.name;
  }
  return undefined;
};

const ProjectTracking = ({ isManagerView = false }: { isManagerView?: boolean }) => {
  const [currentActiveModal, setCurrentActiveModal] = useState<TaskOperationTypes | null>(null);
  const [selectedRow, setSelectedRow] = useState(null);
  const selectedRowData = useMemo(() => (selectedRow !== null ? selectedRow : defaultInputValues), [selectedRow]);
  const [searchInputValue, setSearchInputValue] = useState("");
  const [isTasksLoading, setIsTasksLoading] = useState(false);
  const employeeCode = isManagerView ? extractCode(searchInputValue) : undefined;
  const { data: priorityMasterData } = useMasterData("Priority");
  const { data: taskTypeMasterData } = useMasterData("ProjectTaskType");

  const { formDetails, formErrors, setFormDetail, areFormDetailsValid, setFormDetails } = useForm<any>({
    initialState: selectedRowData,
    validations: formValidators,
    isBulk: false,
  });

  const { data: profileData } = useQuery(["get-profile-data"], {
    queryFn: async () => profileService.fetchProfileData(),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const { data: allTasks, refetch: refetchAllTasks } = useQuery(
    ["all-tasks"],
    async () => projectTrackingService.getTasks(employeeCode),
    {
      enabled: !!employeeCode,
      retryOnMount: false,
      refetchOnWindowFocus: false,
      retry: false,
      onSettled: () => {
        setIsTasksLoading(false);
      },
    },
  );

  const taskOperationMutation = useMutation({
    mutationFn: async (taskType: TaskOperationTypes) => {
      switch (taskType) {
        case "add":
          return await projectTrackingService.addTask(formDetails, employeeCode);
        case "edit":
          return await projectTrackingService.updateTask(formDetails, employeeCode);
        case "duplicate":
          return await projectTrackingService.addTask(formDetails, employeeCode);
        default:
          throw new Error("Invalid task type");
      }
    },
    onSuccess: (isSuccessfullyAdded: boolean) => {
      refetchAllTasks();
      if (isSuccessfullyAdded) {
        setCurrentActiveModal(null);
      }
    },
  });

  useEffect(() => {
    if (!isManagerView) {
      setSearchInputValue("");
      setIsTasksLoading(true);
      refetchAllTasks();
    }
  }, [isManagerView]);

  useEffect(() => {
    if (isManagerView && employeeCode) {
      setIsTasksLoading(true);
    }
  }, [isManagerView, employeeCode]);

  const employeeReporteeOptions =
    profileData?.reportees?.map((item: any) => ({
      name: `${item.display_name} (${item.employee_code})`,
      displayPic: item.display_pic,
      jobTitle: item.job_title,
    })) || [];

  useEffect(() => {
    if (!currentActiveModal) {
      setFormDetails(defaultInputValues);
    }
  }, [currentActiveModal]);

  const isSubmitButtonEnabled = useMemo(() => {
    if (!areFormDetailsValid) return false;
    switch (formDetails.taskType) {
      case "Project":
        return true;
      case "Task":
        return !!formDetails.projectId && !!formDetails.estimatedHours;
      case "SubTask":
        return !!formDetails.projectId && !!formDetails.taskId && !!formDetails.estimatedHours;
      default:
        return false;
    }
  }, [formDetails, areFormDetailsValid]);

  const getInputOptions = useCallback(() => {
    if (formDetails.taskType === "Project") {
      return inputElements.filter(
        (element) => element.name !== "taskId" && element.name !== "projectId" && element.name !== "estimatedHours",
      );
    }
    if (formDetails.taskType === "Task") {
      return inputElements.filter((element) => element.name !== "clientCode" && element.name !== "taskId");
    }
    if (formDetails.taskType === "SubTask") {
      return inputElements.filter((element) => element.name !== "clientCode");
    }
    return inputElements.filter(
      (element) => element.name !== "taskId" && element.name !== "projectId" && element.name !== "estimatedHours",
    );
  }, [formDetails.taskType]);

  const getProjectIdOptions = useCallback(() => {
    return (
      allTasks
        ?.filter((task: any) => task.taskType === "Project")
        ?.map((task: any) => ({ label: task.name, value: task.id })) || []
    );
  }, [allTasks]);

  const getTaskIdOptions = useCallback(() => {
    if (formDetails.taskName) {
      return [
        {
          value: formDetails.parentId,
          label: formDetails.taskName,
        },
      ];
    }
    return (
      allTasks
        ?.filter((task: any) => task.taskType === "Task" && task.parentId === formDetails.projectId)
        ?.map((task: any) => ({ label: task.name, value: task.id })) || []
    );
  }, [formDetails.taskType, allTasks, formDetails.projectId, formDetails.taskName]);

  const getModalConfig = () => {
    if (!currentActiveModal) return null;

    const commonProps = {
      inputElements: getInputOptions() as FormInputType[],
      onChange: setFormDetail,
      formValues: formDetails as unknown as Record<string, unknown>,
      formErrors: formErrors as Record<string, string>,
      isViewOnlyMode: false,
      selectOptions: {
        priority: priorityMasterData?.map((item: any) => ({ label: item, value: item })) || [],
        taskType: taskTypeMasterData?.map((item: any) => ({ label: item, value: item })) || [],
        taskId: getTaskIdOptions(),
        projectId: getProjectIdOptions(),
      },
    };

    switch (currentActiveModal) {
      case "add":
        return {
          ...commonProps,
          modalConfig: {
            isOpen: currentActiveModal === "add",
            setIsOpen: (isOpen: boolean) => setCurrentActiveModal(isOpen ? "add" : null),
            formTitle: "Add Task",
          },
          onSubmitClick: () => taskOperationMutation.mutate("add"),
          formActions: (
            <FormActions
              disabled={!isSubmitButtonEnabled}
              onSubmitClick={() => taskOperationMutation.mutate("add")}
              submitButtonText="Add"
              onCancelClick={() => setCurrentActiveModal(null)}
            />
          ),
        };

      case "edit":
        return {
          ...commonProps,
          modalConfig: {
            isOpen: currentActiveModal === "edit",
            setIsOpen: (isOpen: boolean) => setCurrentActiveModal(isOpen ? "edit" : null),
            formTitle: "Edit Task",
          },
          onSubmitClick: () => taskOperationMutation.mutate("edit"),
          disabledInputFields: { taskType: true },
          formActions: (
            <FormActions
              disabled={!isSubmitButtonEnabled}
              onSubmitClick={() => taskOperationMutation.mutate("edit")}
              submitButtonText="Update"
              onCancelClick={() => setCurrentActiveModal(null)}
            />
          ),
        };

      case "duplicate":
        return {
          ...commonProps,
          modalConfig: {
            isOpen: currentActiveModal === "duplicate",
            setIsOpen: (isOpen: boolean) => setCurrentActiveModal(isOpen ? "duplicate" : null),
            formTitle: "Add Task",
          },
          onSubmitClick: () => taskOperationMutation.mutate("duplicate"),
          disabledInputFields: { taskType: true, projectId: true, taskId: true },
          formActions: (
            <FormActions
              disabled={!isSubmitButtonEnabled}
              onSubmitClick={() => taskOperationMutation.mutate("duplicate")}
              submitButtonText="Add Task"
              onCancelClick={() => setCurrentActiveModal(null)}
            />
          ),
        };

      default:
        return null;
    }
  };

  return (
    <>
      {isManagerView && (
        <Box sx={{ marginBottom: 2, maxWidth: "550px" }}>
          <CustomAutocomplete
            size="small"
            width={"100%"}
            id="search"
            title="Search"
            required={true}
            disabled={false}
            options={employeeReporteeOptions as any}
            searchInputValue={searchInputValue}
            hideInputLabel={true}
            placeholder={"Search projects by employee name"}
            setSearchInputValue={(searchResult: string) => setSearchInputValue(searchResult)}
            isEmployeeView={true}
          />
        </Box>
      )}
      {(!isManagerView || employeeCode) && (
        <TaskTableMRT
          isLoading={isTasksLoading}
          initialData={allTasks || []}
          onCreateTask={() => setCurrentActiveModal("add")}
          onEditClick={(row: any) => {
            const modifiedRow = { ...row, taskName: getParentName(row.parentId, allTasks) };
            setSelectedRow(modifiedRow);
            setCurrentActiveModal("edit");
          }}
          refetchAllTasks={refetchAllTasks}
          addDuplicateTask={(row: any) => {
            setCurrentActiveModal("duplicate");
            setSelectedRow(getChildDetails(row) as any);
          }}
          employeeCode={employeeCode}
        />
      )}
      {currentActiveModal && getModalConfig() && <ModalFormWrapper {...(getModalConfig() as any)} />}
    </>
  );
};

export default ProjectTracking;
