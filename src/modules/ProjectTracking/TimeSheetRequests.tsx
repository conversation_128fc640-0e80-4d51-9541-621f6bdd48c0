import { Chip, Typography } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_ColumnDef } from "material-react-table";
import React, { useState } from "react";
import { TimesheetTracking } from "src/services/api_definitions/timesheetTracking.service";
import { convertToHourMinute } from "src/utils/dateUtils";
import timesheetTrackingService from "../../services/timesheetTracking.service";
import ConfirmationModal from "../Common/Modal/ConfirmationModal";
import TimeSheetLogs from "./TimeSheetLogs";

const getTimesheetStatusColor = (status?: string) => {
  switch (status) {
    case "Sent Back":
      return {
        backgroundColor: "#F44336", //color name is red
        color: "white",
      };
    case "Submitted":
      return {
        backgroundColor: "#FFC107", //color name is yellow
        color: "black",
      };
    case "Approved":
      return { backgroundColor: "#4CAF50", color: "white" }; //color name is green
    default:
      return { backgroundColor: "default", color: "white" };
  }
};

const columns: MRT_ColumnDef<TimesheetTracking>[] = [
  {
    header: "Start Date",
    accessorKey: "start_date",
    size: 120,
  },
  {
    header: "End Date",
    accessorKey: "end_date",
    size: 120,
  },
  {
    header: "Duration",
    accessorKey: "duration",
    size: 110,
  },
  {
    header: "Status",
    accessorKey: "status",
    size: 120,
    Cell: ({ row }) => (
      <Chip
        label={row.original.status}
        size="small"
        sx={{
          width: "100px",
          ...getTimesheetStatusColor(row.original.status),
          fontFamily: "Poppins",
        }}
      />
    ),
  },
  {
    header: "Billable Time",
    accessorKey: "billable_recorded_time",
    size: 120,
    Cell: ({ row }) => {
      return convertToHourMinute(row.original.billable_recorded_time);
    },
  },
  {
    header: "Non-Billable Time",
    accessorKey: "non_billable_recorded_time",
    size: 130,
    Cell: ({ row }) => {
      return convertToHourMinute(row.original.non_billable_recorded_time);
    },
  },
];
const TimeSheetRequests = () => {
  const { data: timesheetRequests, refetch } = useQuery({
    queryKey: ["timesheet-requests"],
    queryFn: () => timesheetTrackingService.getAllTimesheetRequests(),
    refetchOnWindowFocus: false,
  });

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedTimesheetRequestID, setSelectedTimesheetRequestID] = useState<string>("");

  const { mutate: deleteTimesheetRequest } = useMutation({
    mutationFn: (timesheetRequestID: string) => timesheetTrackingService.deleteTimesheetRequest(timesheetRequestID),
    onSuccess: (response) => {
      console.log("response", response);
      if (response) {
        refetch();
      }
    },
  });

  const onRowDeleteIconClick = (timesheetRequestID: string) => {
    setIsDeleteModalOpen(true);
    setSelectedTimesheetRequestID(timesheetRequestID);
  };

  const onDeleteConfirmation = () => {
    deleteTimesheetRequest(selectedTimesheetRequestID);
    setIsDeleteModalOpen(false);
  };

  return (
    <div>
      <TimeSheetLogs
        timesheetRequests={timesheetRequests || []}
        columns={columns}
        enableRowSelection={false}
        onRowSelectionChange={() => {}}
        selectedRows={[]}
        approvalScreen={false}
        onRowDelete={onRowDeleteIconClick}
      />
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onCancel={() => setIsDeleteModalOpen(false)}
        onSubmit={onDeleteConfirmation}
        title="Delete Timesheet Request"
      >
        <Typography>Are you sure you want to delete this timesheet request? This action cannot be undone.</Typography>
      </ConfirmationModal>
    </div>
  );
};

export default TimeSheetRequests;
