import React from "react";
import { useState } from "react";
import { DocumentPreview } from "src/pages/DocumentPreview";
import fileuploaderService from "src/services/fileuploader.service";

const useFilePreview = () => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const handlePreviewClick = async (s3Link: string | null) => {
    if (!s3Link) {
      setPreviewUrl(null);
      return;
    }
    const previewUrl = await fileuploaderService.getDocumentPreviewUrl(s3Link);
    setPreviewUrl(previewUrl);
  };

  return {
    handlePreviewClick,
    previewUrl,
    documentPreview: (
      <DocumentPreview open={!!previewUrl} onClose={() => setPreviewUrl("")} documentUrl={previewUrl || ""} />
    ),
  };
};

export { useFilePreview };
