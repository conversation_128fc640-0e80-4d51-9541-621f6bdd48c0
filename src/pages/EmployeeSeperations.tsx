import { VisibilityOutlined } from "@mui/icons-material";
import { Box, IconButton } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useCallback, useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import DataTable from "src/modules/Common/Table/DataTable";
import { EmployeeSeperations as TypeEmployeeSeperations } from "src/services/api_definitions/offboarding.service";
import offboardingService from "src/services/offboarding.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { toTableHeaderTitleCase } from "src/utils/typographyUtils";
import LeaveBalanceModal from "./LeaveBalanceModal";

const convertLeaveBalance = (row: BaseObject): unknown[] => {
  return Object.keys(row).map((eachRow) => ({
    leave_type: eachRow,
    count: row[eachRow],
  }));
};

const EmployeeSeperations = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState([]);

  const { data: employeeSeperations, isFetched } = useQuery({
    queryKey: ["employee-seperations"],
    queryFn: async () => offboardingService.getEmployeeSeperations(),
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  });

  const onViewLeaveMapClick = (row: BaseObject) => {
    setIsModalOpen(true);
    setSelectedRow((convertLeaveBalance(row) as never[]) || []);
  };

  const columnAccessors = useCallback((row: TypeEmployeeSeperations, key: keyof TypeEmployeeSeperations) => {
    if (typeof row[key] === "boolean") {
      return row[key] ? "Yes" : "No";
    }

    if (key === "leave_balance") {
      return (
        <IconButton color="primary" onClick={() => onViewLeaveMapClick(row?.leave_balance || {})}>
          <VisibilityOutlined />
        </IconButton>
      );
    }
    if (key === "date_of_separation") {
      return formatDateToDayMonthYear(row?.date_of_separation as string);
    }
    return row[key];
  }, []);

  const getColumnsToDisplay = useMemo(() => {
    if (employeeSeperations) {
      const headers = Object.keys(employeeSeperations[0] || {}) as string[];
      return headers.map((header) => ({
        header: toTableHeaderTitleCase(header),
        accessorFn: (row: TypeEmployeeSeperations) => columnAccessors(row, header as keyof TypeEmployeeSeperations),
      }));
    }
    return [];
  }, [employeeSeperations]);

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader title="Employee Separations" subtitle="Work on employee seperations" />
      <DataTable
        data={employeeSeperations || []}
        columns={getColumnsToDisplay}
        state={{
          showSkeletons: !isFetched,
        }}
        enableTopToolbar
      />
      <LeaveBalanceModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        leaveBalanceData={selectedRow || []}
      />
    </Box>
  );
};

export default EmployeeSeperations;
