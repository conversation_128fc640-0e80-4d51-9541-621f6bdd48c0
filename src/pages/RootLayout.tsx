import Box from "@mui/material/Box";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo } from "react";
import { useLocation } from "react-router-dom";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useAuthorizedScreens } from "src/customHooks/useAuthorisedScreens";
import { useContentHeight } from "src/customHooks/useContentHeight";
import { useUserDetails } from "src/customHooks/useUserDetails";
import { useUserSettings } from "src/customHooks/useUserSettings";
import EffiBreadcrumbs from "src/modules/Breadcrumbs/components/EffiBreadcrumbs";
import CandidateOnboardingModal from "src/modules/Employees/Candidate/CandidateOnboarding";
import { forceRefreshToken, getUriPathname } from "src/modules/Login/LoginHook";
import { getJwtRoles, useAuth } from "src/modules/Login/LoginHook";
import { LOCAL_TEST_URI } from "src/modules/Login/constants";

import { PageRoutes } from "src/modules/Routing";
import { PATH_CONFIG } from "src/modules/Routing/config";
import type { TenantDetailsModel } from "src/services/api_definitions/tenants";
import authService from "src/services/auth.service";
import { setSelectedRole, setTenantDetails, setUserRoles } from "src/store/slices/userManagement.slice";
import { getRolePriority } from "src/utils/authUtils";
import { getCookie } from "src/utils/cookieUtils";
import Navigation from "../modules/Navigations/Navigation";
import SelectRoleModal from "./SelectRoleModal";
import { TenantOnboarding } from "./TenantOnboarding";

const RootLayout = () => {
  // defaults and custom hooks
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { isAuthenticated, getTokenFromUrl } = useAuth();
  const sessionToken = localStorage.getItem("accessToken");
  const { selectedRole, userDetails, selectedOrganisation } = useAppSelector((state) => state.userManagement);
  const { tenantDetails } = useAppSelector((state) => state.userManagement);
  const contentHeight = useContentHeight();

  // All of the local application state
  const [isSelectModalOpen, setIsSelectModalOpen] = React.useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = React.useState(() => {
    const ScreenWidth = window.innerWidth;
    return ScreenWidth >= 820;
  });

  // All of the states derived from local state

  const organisationStatus = userDetails?.organisations?.[0]?.status;
  const userRoles = sessionToken ? getJwtRoles(sessionToken, tenantDetails?.tenant_id) : [];
  const isCandidate = userRoles?.includes("Candidate");
  const hasEmployeeAccess = useMemo(() => userRoles?.includes("Employee"), [userRoles]);
  const isLayoutDisabled = [PATH_CONFIG.LOGIN.path].includes(location.pathname);
  const [defaultRole, setDefaultRole] = React.useState<string>("");

  const {
    data,
    isLoading: isTenantDetailsLoading,
    isFetching: isTenantDetailsFetching,
  } = useQuery(
    ["tenant-details-open"],
    async () => {
      const resp: Partial<TenantDetailsModel> = await authService.getTenantDetailsForAuth(
        window.location.hostname.includes("localhost") ? LOCAL_TEST_URI : getUriPathname(),
      );
      dispatch(setTenantDetails(resp as TenantDetailsModel));
      return resp;
    },
    {
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
  );

  /* All of the default API calls which are needed for 
      1. Fetching user roles for organisations
      2. Fetching the screens the author has authorisation to
      3. Fetching the default user settings
  */

  useUserDetails({
    shouldFetch: !!defaultRole,
    defaultRole,
  });

  const allRoles = sessionToken ? getJwtRoles(sessionToken, data?.tenant_id) : [];

  useEffect(() => {
    if (isAuthenticated) {
      forceRefreshToken();
    } else {
      getTokenFromUrl();
    }
  }, [isAuthenticated]);

  const showRoleSelectionModal = localStorage.getItem("showRoleSelectionModal") == "true" && allRoles?.length > 1;

  useAuthorizedScreens({
    shouldFetch: !!selectedRole && !isSelectModalOpen,
  });

  useUserSettings({
    shouldFetch: !!selectedRole && !!selectedOrganisation && hasEmployeeAccess,
  });

  useEffect(() => {
    const showRoleSelectionModal = localStorage.getItem("showRoleSelectionModal") == "true" && allRoles?.length > 1;
    if (showRoleSelectionModal) {
      setIsSelectModalOpen(true);
    }
  }, [sessionToken]);

  useEffect(() => {
    if (sessionToken && data?.tenant_id) {
      const allRoles = getJwtRoles(sessionToken, data?.tenant_id);
      if (allRoles) {
        dispatch(setUserRoles(allRoles));
      }
    }
  }, [sessionToken, data?.tenant_id]);

  useEffect(() => {
    if (isAuthenticated && !isTenantDetailsFetching && !isTenantDetailsLoading) {
      const currentSetRole = getCookie("role");

      setDefaultRole(currentSetRole || getRolePriority(allRoles));

      if (showRoleSelectionModal) {
        setIsSelectModalOpen(true);
      } else {
        dispatch(setSelectedRole(currentSetRole || getRolePriority(allRoles)));
      }
    }
  }, [data, isTenantDetailsFetching, isTenantDetailsLoading, isAuthenticated, selectedRole]);

  return (
    <Box sx={isLayoutDisabled ? {} : { display: "flex" }}>
      {userDetails && isAuthenticated && isCandidate ? (
        <CandidateOnboardingModal />
      ) : (
        <>
          <Navigation isDrawerOpen={isDrawerOpen} setIsDrawerOpen={setIsDrawerOpen} />
          <Box
            display="flex"
            flexDirection="column"
            sx={
              isLayoutDisabled
                ? {}
                : {
                    flexGrow: 1,
                    m: "80px 16px 120px 16px",
                    overflow: "auto",
                  }
            }
          >
            {!isLayoutDisabled && <EffiBreadcrumbs />}
            <Box
              sx={
                isLayoutDisabled
                  ? {}
                  : {
                      flexGrow: 1,
                      p: 3,
                      borderRadius: 4,
                      minHeight: `${contentHeight}px`, // Dynamic height based on calculation
                      background: "white",
                      overflow: "auto",
                      height: "calc(95vh - 80px)",
                    }
              }
            >
              <PageRoutes />
              <TenantOnboarding isOpen={isAuthenticated && organisationStatus === "HR Configuration"} />
              {defaultRole && (
                <SelectRoleModal
                  onClose={() => setIsSelectModalOpen(false)}
                  selectedRole={defaultRole}
                  setSelectedRole={(currentSetRole) => {
                    dispatch(setSelectedRole(currentSetRole));
                  }}
                  isOpen={
                    isSelectModalOpen &&
                    isAuthenticated &&
                    !isTenantDetailsFetching &&
                    !isTenantDetailsLoading &&
                    allRoles.length > 1
                  }
                  roles={allRoles}
                />
              )}
            </Box>
          </Box>
        </>
      )}
    </Box>
  );
};

export default RootLayout;
