import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";
import { EmployeeSeperations, Offboarding, Separation } from "./api_definitions/offboarding.service";

class OffboardingService {
  getNoticePeriodInDays = async () => {
    const resp = await httpClient<BaseResponse<Offboarding>>(apiRegister.OFFBOARDING.paths["notice-period-in-days"]);

    if (resp?.data?.success === false) {
      return null;
    }

    return resp?.data?.response?.notice_period_in_days;
  };

  updateNoticePeriodInDays = async (noticePeriodInDays: number) => {
    const resp = await httpClient<BaseResponse<Offboarding>>(apiRegister.OFFBOARDING.paths["notice-period-in-days"], {
      method: "PUT",
      data: {
        notice_period_in_days: noticePeriodInDays,
      },
    });

    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error updating notice period in days");
    }

    return resp?.data?.response;
  };

  getEmployeeSeperations = async () => {
    const resp = await httpClient<BaseResponse<EmployeeSeperations[]>>(
      apiRegister.OFFBOARDING.paths["get-employee-seperations"],
    );

    if (resp?.data?.errors?.length > 0) {
      return [];
    }

    return resp?.data?.response;
  };

  getProbationPeriodInDays = async () => {
    const resp = await httpClient<BaseResponse<Separation>>(apiRegister.OFFBOARDING.paths["employee-probation-period"]);

    if (resp?.data?.success === false) {
      return null;
    }

    return resp?.data?.response?.probation_period_in_days;
  };

  updateProbationPeriodInDays = async (probationPeriodInDays: number) => {
    const resp = await httpClient<BaseResponse<Separation>>(
      apiRegister.OFFBOARDING.paths["employee-probation-period"],
      {
        method: "PUT",
        data: {
          probation_period_in_days: probationPeriodInDays,
        },
      },
    );

    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error updating probation period in days");
    }

    return resp?.data?.response;
  };
}

export default new OffboardingService();
