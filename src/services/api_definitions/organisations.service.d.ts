export interface OrganisationDetails {
  name: string;
  hr_admin_email: string;
  hr_admin_name: string;
  logo: string | null;
  status: string;
  addresses: Address[];
}

export type Geofence = {
  latitude: number | string | null;
  longitude: number | string | null;
  geofence_radius_meters: number | string | null;
};
export interface Address extends Geofence {
  address_line1: string;
  address_line2: string;
  city: string;
  state: string;
  country: string;
  zip_code: string;
  geofence_enabled: boolean;
}

export type CreateOrganisationDetail = Partial<Omit<OrganisationDetails, "status">>;
export type UpdateOrganisationDetail = Omit<OrganisationDetails, "status", "addresses"> & {
  new_name: string;
  new_hr_admin_email: string;
  new_hr_admin_name: string;
  new_addresses: Address[];
};
