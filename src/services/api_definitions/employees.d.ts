// employeeTypes.ts

import { PayrollTemplate } from "./payroll.service";

interface EmployeeDetails {
  business_unit: string;
  department: string;
  display_name: string;
  employee_code: string;
  employee_type: string;
  employment_status: string;
  manager: Manager;
  organisation: string;
  date_of_birth: string;
  gender: string;
  display_pic?: string;
  email: string;
  date_of_joining: string;
  blood_group: string;
  location: string;
  office_address: EntAddress;
  reportees: Reportee[];
  tenure: string;
  first_name: string;
  last_name: string;
  personal_email: string;
  phone: {
    country_code: string;
    number: string;
  };
  date_of_confirmation: string;
  nationality: string;
  marital_status: string;
  aadhaar: string;
  pan: string;
  passport: string;
  uan: string;
  current_address: EntAddress;
  permanent_address: EntAddress;
  bank_account: BankAccount;
  work_role: WorkRole;
  emergency_contacts: EmergencyContact[];
  work_experience: WorkExperience[];
  dependents: Dependent[];
  education_details: EducationDetail[];
  business_unit: string;
  hrbp?: string;
  job_title: string;
  department: string;
  cost_center: string;
  compensations: PayrollTemplate[];
  current_compensation: PayrollTemplate;
  form_status: string;
  action_menu: string[];
  leave_balance: Record<string, number>;
}
export interface LeaveBalanceItems {
  leave_type: string;
  count: number;
}
export interface EducationDetail {
  institute: string;
  university: string;
  degree: string;
  degree_type: string;
  start_year: Year;
  end_year: Year;
  employee_document: EmployeeDocument;
}

export interface BankAccount {
  account_number: string;
  bank_name: string;
  ifsc: string;
  bank_branch: string;
  account_holder_name: string;
  // address: Record<string, never>;  does not contain address
}

export interface EntAddress {
  house: string;
  street: string;
  city: string;
  state: string;
  country: string;
  zip_code: string;
  landmark: string;
  display_address: string;
}

export interface Dependent {
  first_name: string;
  last_name: string;
  date_of_birth: Date | string;
  relation: string;
}

export interface EmergencyContact {
  name: string;
  phone: {
    country_code: string;
    number: string;
  };
  relation: string;
  primary: boolean;
}

export interface JobTitle {
  name: string;
  work_role: WorkRole;
  department: Department;
}

export interface Department {
  name: string;
  business_unit: BusinessUnit;
}

interface BusinessUnit {
  name: string;
  description: string;
  organisation: BusinessUnitOrganisation;
  cost_center: CostCenter;
}

interface CostCenter {
  code: string;
  tenant_id: string;
}

interface BusinessUnitOrganisation {
  id: number;
  name: string;
  hr_admin_email: string;
  hr_admin_name: string;
}

interface WorkRole {
  band: Band;
  level: Band;
  grade: Band;
  name: string;
}

interface Band {
  name: string;
}

interface Manager {
  id: string;
  display_name: string;
  manager: Manager;
}

interface Reportee {
  id: string;
  employee_code: string;
  display_name: string;
}

export interface WorkExperience {
  company: string;
  designation: string;
  from_date: Date;
  to_date: Date;
}

interface TransformedEmployee {
  [key: string]: string; // Add index signature to allow dynamic key access
  leave_balance: Record<string, number>;
}

interface EmployeeOffboardingDetailsResposnse {
  name: string;
  job_title: string;
  department: string;
  employee_code: string;
  approver_name: string;
  resignation_date: string;
  last_working_date: string;
  desired_last_working_date: string;
  approved_last_working_date: string;
  separation_reason: string;
  salary_on_hold: boolean;
  notice_period_waived_off: boolean;
  notice_period: string;
  description: string;
  request_status: {
    approver_role: string;
    comments: string;
    status: string;
    approved_last_working_date: string;
    salary_on_hold: boolean;
    notice_period_waived_off: boolean;
  }[];
}

export interface EmployeeOffboardingTableResposnse {
  name: string;
  job_title: string;
  display_pic: string;
  department: string;
  employee_code: string;
  approver_name: string;
  resignation_date: string;
  last_working_date: string;
  desired_last_working_date: string;
  approved_last_working_date: string;
  separation_reason: string;
  salary_on_hold: boolean;
  notice_period_waived_off: boolean;
  notice_period: string;
  description: string;
  status: string;
  request_status: RequestStatus[];
}

export type RequestStatus = {
  approver_role: string;
  comments: string;
  status: string;
  approved_last_working_date: string;
  salary_on_hold: boolean;
  notice_period_waived_off: boolean;
  actioned_at: string | null;
};

export type OffboardingSummary = {
  total_employees: number;
  resignation_approval_pending: number;
  on_notice_period: number;
  checklist_completion_pending: number;
};

export type EmployeeJourney = {
  event_type: string;
  event_date: string;
  event_details: string;
  old_value: string;
  new_value: string;
};

export {
  EmployeeDetails,
  BankAccount,
  EntAddress,
  Dependent,
  EmergencyContact,
  JobTitle,
  Department,
  BusinessUnit,
  CostCenter,
  BusinessUnitOrganisation,
  WorkRole,
  Band,
  Manager,
  Reportee,
  WorkExperience,
  TransformedEmployee,
  EmployeeOffboardingDetailsResposnse,
};
