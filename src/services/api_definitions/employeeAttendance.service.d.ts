import { Attendance, TimeSheet } from "./timesheets.service";

export type TimeSheetStatus = {
  event_type: "Attendance" | "Leave";
  status: string;
  paid?: boolean;
};
export interface ActivityLogDetails {
  login_date: string;
  check_in_time: string;
  check_out_time: string;
  status: string;
  timesheet_status: TimeSheetStatus[];
  location: string;
  duration: string;
  details: Attendance[];
}
export interface RegularisationRequest {
  request_id: string;
  reason: string;
  comment: string;
  status: string;
  raised_on: Date;
  raised_by: RaisedBy;
  approver: string;
  check_in_time: string;
  check_out_time: string;
  applied_date: string;
  duration: string;
  details: Attendance[];
  leave_type: string;
}

export interface RaisedBy {
  employee_code: string;
  display_name: string;
  job_title: string;
  display_pic: string;
}

export interface AttendanceConfiguration {
  default_config: DefaultConfig;
  custom_configs: DefaultConfig[];
}

export interface DefaultConfig {
  num_working_days: number;
  work_start_time: string;
  work_end_time: string;
  week_offs: string[];
  enforce_working_hours: boolean;
  min_hours_half_day: string | null;
  min_hours_full_day: string | null;
  working_hours_lapse_enforcement: string;
  business_unit_name?: string;
  department_names?: string[];
  start_date?: string | null;
  end_date?: string | null;
}

export interface EmployeeAttendanceConfiguration {
  current: DefaultConfig | null;
  future: DefaultConfig | null;
}
