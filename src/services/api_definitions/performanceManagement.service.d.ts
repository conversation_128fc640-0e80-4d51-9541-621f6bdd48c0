type EmployeeDetail = {
  employee_name: string;
  job_title: string;
  display_pic: string;
  employee_code: string;
  business_unit: string;
  department: string;
};

export type PerformanceReviewRequest = EmployeeDetail & {
  review: PerformanceReview;
};

export type EmployeeGoalSettingDetail = EmployeeDetail & {
  goal: Goal;
};

type JobTitle = {
  name: string;
  band: string;
  level: string;
  grade: string;
  department: string;
  business_unit: string;
  work_role_name: string;
  formatted_name: string;
};
export interface PerformanceReview {
  review_id?: string;
  performance_review_cycle?: PerformanceReviewCycle;
  status?: string;
  comment?: string;
  anonymous?: boolean;
  goal_objective_feedbacks?: GoalObjectiveFeedbacks[];
  submitted_at: string;
  promotion: {
    justification?: string;
    job_title?: JobTitle;
    status?: "Pending" | "Approved" | "Rejected";
    comments?: string;
    effective_date?: string;
  } | null;
}

type ReviewerTypes = "self" | "manager" | "peer" | "stakeholder" | "hrbp";

export type Feedback = {
  id: string;
  reviewer_type: ReviewerTypes;
  comments?: string;
  display_rating?: string;
  rating_value?: number;
  rating_description?: string;
  reviewer_name?: string;
  reviewer_job_title?: string;
};
export interface GoalObjectiveFeedbacks {
  goal_objective?: Objective;
  feedbacks: Feedback[];
}

export type ReviewCycle = {
  name: string;
  start_date: string;
  end_date: string;
  goal_setting_start_date: string;
  goal_setting_end_date: string;
  performance_review_start_date: string;
  performance_review_end_date: string;
  status?: string;
  goal_setting_enabled: boolean;
  performance_review_enabled: boolean;
};

export type UpdateReviewCycle = ReviewCycle & {
  new_name?: string;
};

export type RatingsConfiguration = {
  value: number;
  description: string;
  display_rating?: string;
};

export type Requests = EmployeeDetail & {
  goal: Goal;
};

export interface Goal {
  goal_id: string;
  performance_review_cycle?: PerformanceReviewCycle;
  status: string;
  comment?: string;
  enabled: boolean;
  approved_at: string | null;
  objectives: Objective[];
}

interface BaseObjective {
  title: string;
  description: string;
  target_completion_date: string;
  status: string;
  deprioritisation_reason: string | null;
}

export type LinkedGoalObjective = BaseObjective & {
  goal_objective_id: string;
  owner_name: string;
};

export interface Objective extends BaseObjective {
  id?: string;
  status?: string;
  actual_completion_date?: string;
  estimated_weightage?: number;
  final_weightage?: number;
  linked_goal_objective_id: string | null;
  deprioritisation_reason: string;
  linked_goal_objective: LinkedGoalObjective;
}

export interface PerformanceReviewCycle {
  name: string;
  goal_setting_enabled: boolean;
  performance_review_enabled: boolean;
  status?: string;
}

export interface PeerNomination {
  nominee_email: string;
  review_status: string;
}

export interface PeerNominations {
  review_id: string;
  performance_review_cycle: PerformanceReviewCycle;
  status: string;
  peer_nominations: PeerNomination[];
}

export interface PeerNominationRequest extends EmployeeDetail {
  peer_nomination: PeerNominations;
}
