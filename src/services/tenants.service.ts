import { format } from "date-fns";
import { apiRegister } from "src/services";
import {
  ValidFileExtensions,
  createPseudoLinkAndDownload,
  getFilenameFromContentDisposition,
} from "src/utils/fileUtils";
/* eslint-disable no-useless-catch */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from "src/utils/httpClient";
import { BaseResponse, FileUploadResponse } from "./api_definitions/default.service";
import { WorkRole } from "./api_definitions/employees";
import {
  CancelSubscription,
  CostCenterDetail,
  CostCenterDetailResponse,
  IntegrationScreen,
  TenantDetailsModel,
  TenantSubscription,
  TenantUpdateRequest,
  WorkRoleDetails,
} from "./api_definitions/tenants";
import { injectParams } from "./data_transformers/tenants.transform";
import fileuploaderService from "./fileuploader.service";

class TenantsService {
  getTenants = async () => {
    try {
      const response = await httpClient<BaseResponse<TenantDetailsModel[]>>(
        apiRegister.TENANTS.paths["get-tenant-details"],
      );

      if (!response.data.success) {
        return [];
      }

      if (response.data.errors && response.data?.errors?.length > 0) {
        return [];
      }
      return injectParams(response.data.response);
    } catch (_err) {
      return [];
    }
  };

  getTenant = async (id: string): Promise<TenantDetailsModel | null> => {
    try {
      const response = await httpClient<BaseResponse<TenantDetailsModel>>(apiRegister.TENANTS.paths["get-tenant"], {
        params: {
          id,
        },
      });

      if (!response.data.success) {
        return null;
      }

      if (response.data.errors && response.data?.errors?.length > 0) {
        return null;
      }
      return response.data.response;
    } catch (_err) {
      return null;
    }
  };

  getCostCenterDetails = async (tenantId?: string): Promise<CostCenterDetailResponse> => {
    try {
      const resp = await httpClient<BaseResponse<CostCenterDetailResponse>>(
        apiRegister.TENANTS.paths["get-cost-center-details"],
        {
          params: tenantId && { tenant_id: tenantId },
        },
      );
      return resp.data.response;
    } catch (_err) {
      return [];
    }
  };

  getWorkRoleDetails = async (tenantId: string): Promise<WorkRoleDetails[]> => {
    try {
      const resp = await httpClient<BaseResponse<WorkRoleDetails[]>>(
        apiRegister.TENANTS.paths["get-work-role-details"],
        {
          params: {
            tenant_id: tenantId,
          },
        },
      );
      return resp.data.response;
    } catch (_err) {
      return [];
    }
  };

  downloadSampleWorkRoleTemplate = async () => {
    try {
      const resp = await httpClient<Blob>(apiRegister.TENANTS.paths["download-sample-template"], {
        responseType: "blob",
      });

      const [fileName, extention] = (
        getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
      ).split(".");

      if (fileName && extention) {
        createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
      }
      return [];
    } catch (_err) {
      return [];
    }
  };

  uploadFile = async (file: File, tenantId: string) => {
    try {
      const formData = new FormData();
      formData.append("file", file);
      const resp: FileUploadResponse<string | string[]> = await fileuploaderService.uploadFile(
        apiRegister.TENANTS.paths["upload-work-roles"],
        formData,
        {
          tenant_id: tenantId,
        },
      );
      if (resp !== undefined && resp.type === "error") throw resp;
      else return resp;
    } catch (err) {
      return err;
    }
  };

  saveCostCenters = async (tenantId: string, costCenters: CostCenterDetail[]) => {
    try {
      const resp = await httpClient<string>(apiRegister.TENANTS.paths["create-cost-centers"], {
        method: "POST",
        params: {
          tenant_id: tenantId,
        },
        data: costCenters,
      });

      return resp.data;
    } catch (err) {
      console.log({ err });
    }
  };

  updateTenantUrl = async (tenantId: string, url: string) => {
    try {
      const resp = await httpClient<string>(apiRegister.TENANTS.paths["tenant-update"], {
        params: { tenant_id: tenantId },
        method: "PATCH",
        data: {
          tenant_url: url,
        },
      });

      return resp.data;
    } catch (err) {
      console.log({ err });
    }
  };

  getSelectedTenantDetails = async ({ tenantId }: { tenantId: string }): Promise<TenantDetailsModel> => {
    const endpoint = apiRegister.TENANTS.paths["get-selected-tenant-details"].replace(":tenantId", tenantId);
    const resp = await httpClient<BaseResponse<TenantDetailsModel>>(endpoint);
    return resp.data.response;
  };

  updateTenant = async (tenantId: string, payload: TenantUpdateRequest) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TENANTS.paths["update-tenant"], {
      method: "PATCH",
      params: {
        tenant_id: tenantId,
      },
      data: payload,
    });

    return resp?.data?.response;
  };

  addWorkRole = async (payload: WorkRole) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TENANTS.paths["add-work-role"], {
      method: "POST",
      data: { ...payload },
    });
    return resp?.data?.response;
  };

  deleteWorkRole = async (name: string) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TENANTS.paths["delete-work-role"], {
      method: "DELETE",
      data: {
        name,
      },
    });
    return resp?.data?.response;
  };

  deleteTenant = async (tenantId: string) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TENANTS.paths["delete-tenant"], {
      method: "DELETE",
      params: {
        tenant_id: tenantId,
      },
    });
    return resp?.data?.response;
  };

  getTenantIntegrations = async (tenantId: string) => {
    const resp = await httpClient<BaseResponse<IntegrationScreen[]>>(
      apiRegister.TENANTS.paths["get-tenant-integrations"],
      {
        method: "GET",
        params: {
          tenant_id: tenantId,
        },
      },
    );

    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching tenants");
    }
    return resp?.data?.response;
  };

  updateTenantIntegrations = async (tenantId: string, screenName: string, redirectUrl: string) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TENANTS.paths["get-tenant-integrations"], {
      method: "PUT",
      params: {
        tenant_id: tenantId,
      },
      data: [
        {
          screen_name: screenName,
          redirect_url: redirectUrl,
        },
      ],
    });

    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error while updating integrations");
    }
    return resp?.data?.response;
  };

  getTenantSubscriptions = async (tenantId: string) => {
    const resp = await httpClient<BaseResponse<TenantSubscription[]>>(
      apiRegister.TENANTS.paths["get-tenant-subscriptions"],
      {
        method: "GET",
        params: {
          tenant_id: tenantId,
        },
      },
    );

    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching subscriptions");
    }
    return resp?.data?.response;
  };

  createTenantSubscription = async (tenantId: string, payload: Record<string, any>) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TENANTS.paths["create-tenant-subscription"], {
      method: "POST",
      params: {
        tenant_id: tenantId,
      },
      data: {
        ...payload,
      },
    });

    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching subscriptions");
    }
    return resp?.data?.response;
  };

  cancelSubscription = async (payload: CancelSubscription) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TENANTS.paths["cancel-tenant-subscription"], {
      method: "PUT",
      data: {
        ...payload,
        effective_date: format(payload?.effective_date, "yyyy-MM-dd"),
      },
    });

    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching subscriptions");
    }
    return resp?.data?.response;
  };
}

export default new TenantsService();
