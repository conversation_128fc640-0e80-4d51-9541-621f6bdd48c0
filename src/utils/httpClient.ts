import { nanoid } from "@reduxjs/toolkit";
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse, HttpStatusCode } from "axios";
import { checkAndRefreshToken } from "src/modules/Login/LoginHook";
import { LOCAL_TEST_URI } from "src/modules/Login/constants";
import { BaseResponse } from "src/services/api_definitions/default.service";
import { getCookie } from "./cookieUtils";
import { getTimezone } from "./dateUtils";
import { showToast } from "./toast";

const axiosInstance = axios.create({
  baseURL: process.env.REACT_APP_URL,
});

const handleNetworkErrors = (status: HttpStatusCode, message: string) => {
  const regex = /^4\d{2}$/;
  if (regex.test(status.toString())) {
    showToast(message, {
      type: "error",
    });
    return;
  }
  showToast("Something went wrong!", {
    type: "error",
  });
};

axiosInstance.interceptors.request.use(async (config) => {
  const token = await checkAndRefreshToken();
  const isOpenTenantApi = config.url?.includes("/tenant/open");
  if (token && !isOpenTenantApi) {
    config.headers.Authorization = `Bearer ${token}`;
    config.headers.set("X-Request-ID", nanoid());
    config.headers.set("X-Role-ID", getCookie("role"));
    config.headers.set("X-Org-ID", getCookie("org"));
    config.headers.set("X-Timezone-ID", getTimezone());
    config.headers.set("X-Auth-Provider", "native");

    if (window.location.host.includes("localhost")) {
      config.headers.set("X-Origin", LOCAL_TEST_URI);
    }
  }
  return config;
});

// centralised error handling
axiosInstance.interceptors.response.use(
  (response: AxiosResponse<BaseResponse<unknown>>) => {
    if (response?.data?.success_messages?.length > 0) {
      response?.data?.success_messages?.forEach((message: string) =>
        showToast(message, {
          type: "success",
        }),
      );
      return response;
    }
    if (response?.data?.errors?.length > 0) {
      response?.data?.errors?.forEach((error: string) =>
        showToast(error, {
          type: "error",
        }),
      );
      return response;
    }
    return response;
  },
  (error: AxiosError) => {
    const { response } = error;
    handleNetworkErrors(response?.status as HttpStatusCode, response?.data as string);
    return error;
  },
);

const httpClient = async <T>(
  url: string,
  options: AxiosRequestConfig<unknown> = {},
): Promise<AxiosResponse<T, unknown>> => {
  return axiosInstance(url, {
    ...options,
  });
};

export { axiosInstance, httpClient };
