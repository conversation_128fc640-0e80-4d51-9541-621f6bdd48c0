const getLeaveStatusColorsByPaidStatus = (paid?: boolean) => {
  if (paid) {
    return "rgb(0, 127, 111)"; // Green
  }
  return "#FF4E4E"; // Red
};

const getStatusColors = (status: string) => {
  if (!status) {
    return "#66705";
  }
  switch (status.toLowerCase()) {
    case "pending":
    case "regularisation pending":
    case "not started":
    case "cancellation requested":
    case "on notice":
      return "#F49025"; // Orange
    case "in progress":
    case "regularisation requested":
    case "draft":
    case "probation":
      return "#619FFC"; // Blue
    case "week off":
      return "rgb(102, 112, 133)"; // Greyish
    case "holiday":
      return "rgb(3, 155, 229)"; // Blue
    case "absent":
    case "rejected":
    case "sent back":
    case "inactive":
    case "terminated":
    case "suspended":
    case "canceled":
    case "lop - full day":
    case "lop - half day":
    case "loss of pay (full)":
    case "rescinded":
    case "denied":
    case "loss of pay (half)":
    case "on leave (w/o pay)":
    case "disabled":
    case "deprioritised":
    case "0.5 absent":
      return "#FF4E4E"; // Red
    case "regularised":
    case "approved":
    case "submitted":
    case "active":
    case "present":
    case "on leave":
    case "accepted":
    case "completed":
    case "enabled":
    case "open":
    case "0.5 present":
      return "rgb(0, 127, 111)"; // Green
    // New holiday types
    case "national":
      return "#00308F"; // Dark blue
    case "religious":
      return "#8B0000"; // Dark red
    case "regional":
      return "#006400"; // Dark green
    case "cultural":
      return "#4B0082"; // Indigo
    case "commemorative":
      return "#800080"; // Purple
    case "custom":
      return "#B8860B"; // Dark goldenrod
    case "in office":
      return "#004B23"; // Very Dark Green - accessible (7:1)
    case "wfh":
      return "#003366"; // Very Dark Blue - accessible (7:1)
    case "in on-site":
      return "#B34700"; // Dark Burnt Orange - accessible (7:1)
    case "checked-out":
      return "#4A4A4A"; // Dark Grey - accessible (7:1)
    case "not checked-in":
      return "#8B0000"; // Dark Red - accessible (7:1)
    // Event icons' colors
    case "joining day":
      return "#4caf50"; // Green
    case "probation confirmation":
      return "#1976d2"; // Blue
    case "promotion":
      return "#7b1fa2"; // Purple
    case "resignation":
      return "#9e9e9e"; // Grey
    case "manager change":
      return "#fbc02d"; // Yellow
    case "department change":
      return "#ff9800"; // Orange
    case "last working day":
      return "#d32f2f"; // Red
    case "work anniversary":
      return "#f57c00"; // Orange
    case "employee type change":
      return "#f57c00"; // Orange
    default:
      return "black";
  }
};

const toTableHeaderTitleCase = (str: string) => {
  // Replace underscores with spaces, split the string into words
  const words = str.replace(/_/g, " ").split(" ");

  // Capitalize the first letter of each word
  const titleCaseWords = words.map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase());

  // Join the words back into a string
  return titleCaseWords.join(" ");
};

export { getStatusColors, toTableHeaderTitleCase, getLeaveStatusColorsByPaidStatus };
