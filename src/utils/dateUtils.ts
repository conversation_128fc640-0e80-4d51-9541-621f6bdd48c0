import { add, addDays, addHours, differenceInMinutes, differenceInSeconds, format, parse, parseISO } from "date-fns";
import { fromZonedTime, toZonedTime } from "date-fns-tz";
import { MONTHS } from "src/app/constants";
import { BaseObject, Option } from "src/app/global";

export const DD_MM_YYYY = "dd MMM yyyy";
export const DD_MM_YYYY_STYLED_HYPHEN = "dd-MM-yyyy";

export const formatDateToDayMonthYear = (dateTimeStr: string | null): string => {
  if (!dateTimeStr) {
    return "N/A";
  }
  const date = parse(dateTimeStr, "yyyy-MM-dd", new Date());
  return format(date, "dd MMM yyyy");
};

export const formatDateToDayMonth = (dateTimeStr: string): string => {
  const date = parse(dateTimeStr, "yyyy-MM-dd", new Date());
  return format(date, "dd MMM");
};

export const formatDateTime = (dateTimeStr: string): string => {
  const date = new Date(dateTimeStr);
  return format(date, "do MMM, yyyy");
};

export const formatDateNormal = (dateTimeStr: string): string => {
  const date = new Date(dateTimeStr);
  return format(date, "yyyy-MM-dd");
};

export const formatDate = (dateString: string) => {
  const date = parse(dateString, "yyyy-MM-dd", new Date());
  return format(date, "MMM, yyyy");
};

const getTime = (time: string) => {
  return time ? parse(time, "HH:mm", 1) : "";
};

export const getTimeDifference = (time1: string, time2: string): string => {
  if (!time1 || !time2) {
    return "";
  }

  const parsedTime1 = getTime(time1);
  const parsedTime2 = getTime(time2);

  // Handle time spans across midnight
  if (parsedTime1 > parsedTime2) {
    const totalDiffMin = differenceInMinutes(addHours(parsedTime2, 24), parsedTime1);
    const totalDiffHr = Math.floor(totalDiffMin / 60);
    const diffMin = totalDiffMin % 60;
    return `${totalDiffHr}:${diffMin}`;
  }
  const totalDiffMin = differenceInMinutes(parsedTime2, parsedTime1);
  const totalDiffHr = Math.floor(totalDiffMin / 60);
  const diffMin = totalDiffMin % 60;
  return `${totalDiffHr}:${diffMin}`;
};

export const getMinutesDifference = (time1: string, time2: string): number => {
  return differenceInMinutes(getTime(time1), getTime(time2));
};

export const convertHoursToMinutes = (timeInHour: string): number => {
  const timeParts = timeInHour?.split(":");

  if (!timeParts || timeParts.length !== 2) {
    return 0;
  }
  return Number(timeParts[0]) * 60 + Number(timeParts[1]);
};

export const compareTwoDatesWithoutTime = (date1: Date, date2: Date): boolean => {
  date1.setHours(0, 0, 0, 0);
  date2.setHours(0, 0, 0, 0);
  return date1?.getTime() === date2?.getTime();
};

export const generateMonthYearList = (years: string[]): Option<string, string>[] => {
  return years.flatMap((year) =>
    MONTHS.map((month, index) => ({
      label: `${month} ${year}`,
      value: `${String(index + 1).padStart(2, "0")}-${year}`,
    })),
  );
};

export const generateMonthYearLables = (years: string[]): string[] => {
  return years.flatMap((year) => MONTHS.map((month) => `${month} ${year}`));
};
export const getListOfDaysInMonth = (month: number, year: number) => {
  const days = [];

  for (let i = 1; i <= new Date(year, month, 0).getDate(); i++) {
    days.push(format(new Date(year, month - 1, i), "MM/dd/yyyy"));
  }
  return days;
};

export const getTodaysDate = (dateFormat = "PPP") => {
  return format(new Date(), dateFormat);
};

const formatDuration = (minute: number) => {
  const hours = Math.floor(minute / 60);
  const remainingMinutes = minute % 60;
  return `${hours.toString().padStart(2, "0")}:${remainingMinutes.toString().padStart(2, "0")} Hrs`;
};

/**
 *
 * @param checkInTime
 * @param standardCheckOutTime
 * @param totalDeltaInMinutes
 * @param standardCheckInTime
 * @returns Time delta and progress
 * @deprecated
 */
export const calculateCheckInCheckOut = (
  checkInTime: string,
  standardCheckOutTime: string,
  totalDeltaInMinutes: string,
  standardCheckInTime: string,
) => {
  const today = new Date();
  const checkIn = parse(checkInTime, "HH:mm", today);
  const deltaInMin = convertHoursToMinutes(totalDeltaInMinutes);

  // Calculate the time difference from the adjusted check-in to now
  const diffInMinutes = differenceInMinutes(today, checkIn);

  const timeDelta = formatDuration(Math.max(diffInMinutes, 0) + deltaInMin);
  // Calculate progress that is the expected totalWorkdayMinutes/totalWorkedMinutes

  if (!standardCheckOutTime || !standardCheckInTime) {
    return {
      timeDelta,
      progress: 100,
    };
  }
  const standardCheckOut = parse(standardCheckOutTime, "HH:mm", today);
  const standardCheckIn = parse(standardCheckInTime, "HH:mm", today);
  const totalWorkDayMinutes = differenceInMinutes(standardCheckOut, standardCheckIn);
  const totalWorkedMinutes = deltaInMin + Math.max(diffInMinutes, 0);
  const progress = Math.min(totalWorkedMinutes / totalWorkDayMinutes, 1);

  return {
    timeDelta,
    progress: progress * 100,
  };
};

export const parseTime = (time: string) => {
  const [hours, minutes] = time.split(":").map(Number);
  return hours * 60 + minutes;
};

export const getIntlTimeToSpecifiedFormat = (date: Date | string | null, specifiedFormat: string) => {
  if (!date) {
    return {
      parsedDate: null,
      formattedDate: "--:--",
    };
  }

  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const parsedDate = toZonedTime(parseISO(date as string), timeZone);
  return {
    parsedDate,
    formattedDate: format(parsedDate, specifiedFormat),
  };
};

export const convertTimeToAMPMWithZonedTime = (timeString: string, defaultLabel: string = "--:--") => {
  if (!timeString) {
    return defaultLabel;
  }
  return getIntlTimeToSpecifiedFormat(timeString, "hh:mm a").formattedDate;
};

export const convertTimeToAMPM = (timeString: string, defaultLabel: string = "--:--") => {
  if (!timeString) {
    return defaultLabel;
  }
  const date = parse(timeString, "HH:mm", new Date());
  return format(date, "h:mm a");
};

export const getTimezone = () => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

export const convertToZonedDateTime = (date: Date) => {
  const utcDate = fromZonedTime(date, getTimezone());
  return utcDate.toISOString();
};

export const addTimeInDate = (date: Date, time: BaseObject) => {
  return add(date, time);
};

export const getGMTOffset = () => {
  const date = new Date();
  const offsetInMinutes = date.getTimezoneOffset();

  const offsetHours = Math.floor(Math.abs(offsetInMinutes) / 60);
  const offsetMinutes = Math.abs(offsetInMinutes) % 60;

  const sign = offsetInMinutes <= 0 ? "+" : "-";
  const formattedOffsetHours = String(offsetHours).padStart(2, "0");
  const formattedOffsetMinutes = String(offsetMinutes).padStart(2, "0");

  return `GMT${sign}${formattedOffsetHours}:${formattedOffsetMinutes}`;
};

export const isBeforeToday = (dateStr: string) => {
  const date = new Date(dateStr);
  const today = new Date();

  // Set both dates to midnight for accurate comparison
  date.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);

  return date < today;
};

// TODO: Refactor this function to use generic formats throughout
export const getDateFormat = (views: string[]) => {
  if (views.length === 3) {
    return DD_MM_YYYY_STYLED_HYPHEN;
  }
  return undefined;
};

export function getCalendarDayDifference(date1: string, date2: string) {
  const d1 = new Date(date1);
  const d2 = new Date(date2);

  // Strip the time portion to focus on calendar days only
  const d1StartOfDay = new Date(d1.getFullYear(), d1.getMonth(), d1.getDate());
  const d2StartOfDay = new Date(d2.getFullYear(), d2.getMonth(), d2.getDate());

  // Calculate the difference in days
  const diffInMs = Math.abs(d2StartOfDay.getTime() - d1StartOfDay.getTime());
  const calendarDays = diffInMs / (1000 * 60 * 60 * 24);

  return calendarDays;
}

// Define the input type for the function
interface CalculateNightShiftProgressParams {
  totalDuration: string; // Total duration in seconds
  shiftStartTime: string; // Time in "HH:mm" format
  shiftEndTime: string; // Time in "HH:mm" format
  lastCheckinTimestamp: string; // ISO format timestamp
  specifiedFormat?: string; // Optional format string, default is "HH:mm:ss"
}

// Define the output type for the function
interface CalculateNightShiftProgressResult {
  formattedTime: string; // Elapsed time in "HH:mm" format
  seconds: number; // Total seconds elapsed
  progress: number; // Progress percentage (0-100)
}

// Helper function to parse "hh:mm" string into seconds
function parseDurationToSeconds(duration: string): number {
  const [hours, minutes, seconds = 0] = duration.split(":").map(Number); // Default seconds to 0 if not provided
  return hours * 3600 + minutes * 60 + seconds;
}

// Helper function to convert seconds into "HH:mm:ss"
function formatSecondsToHHMMSS(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}:${String(secs).padStart(2, "0")}`;
}

export const convertToHourMinute = (timeStr: string) => {
  if (!timeStr) {
    return "";
  }

  return timeStr
    .split(":")
    .map((clockEntry, index) => (index === 0 ? `${clockEntry}h` : `${clockEntry}m`))
    .join(" ");
};

export function calculateNightShiftProgress({
  totalDuration,
  shiftStartTime,
  shiftEndTime,
  lastCheckinTimestamp,
  specifiedFormat = "HH:mm:ss",
}: CalculateNightShiftProgressParams): CalculateNightShiftProgressResult {
  // Parse the current date
  const currentDate = new Date();

  // Combine shift times with the current date, defaulting to full-day shifts if null
  const shiftStart = shiftStartTime
    ? parse(shiftStartTime, "HH:mm", currentDate)
    : parse("00:00", "HH:mm", currentDate);
  const shiftEnd = shiftEndTime ? parse(shiftEndTime, "HH:mm", currentDate) : parse("23:59", "HH:mm", currentDate);

  // Handle shifts spanning midnight by adding a day to the end time
  const adjustedEnd = shiftEnd < shiftStart ? addDays(shiftEnd, 1) : shiftEnd;

  // Use getIntlTimeToSpecifiedFormat for lastCheckin
  const { parsedDate: lastCheckin } = getIntlTimeToSpecifiedFormat(lastCheckinTimestamp, specifiedFormat);

  // Derive current time
  const { parsedDate: currentTime } = getIntlTimeToSpecifiedFormat(new Date().toISOString(), specifiedFormat);

  // Ensure parsed dates are valid
  if (!lastCheckin || !currentTime) {
    throw new Error("Invalid date parsing in getIntlTimeToSpecifiedFormat");
  }

  // Parse totalDuration (hh:mm) into seconds
  const initialDurationInSeconds = parseDurationToSeconds(totalDuration);

  // Calculate time delta in seconds
  const elapsedTime = differenceInSeconds(currentTime, lastCheckin);
  const updatedTotalDuration = initialDurationInSeconds + elapsedTime;

  // Total shift duration
  const totalShiftDuration = differenceInSeconds(adjustedEnd, shiftStart);

  // Format total elapsed time in "HH:mm:ss"
  const formattedTime = formatSecondsToHHMMSS(updatedTotalDuration);

  // Extract the seconds (0-59) component from the formatted time
  const seconds = updatedTotalDuration % 60;

  // Calculate progress percentage
  const progress = totalShiftDuration ? Math.min((updatedTotalDuration / totalShiftDuration) * 100, 100) : 0; // Avoid division by zero

  return {
    formattedTime, // Time in "HH:mm:ss" format
    seconds, // Exact seconds (0-59)
    progress, // Progress percentage,
  };
}
