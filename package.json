{"name": "ui-effi-hr", "version": "14.0.0", "description": "This project is for our spectacular SAAS", "main": "src/index.js", "scripts": {"test": "npm run test", "build": "cross-env NODE_ENV=production webpack --config ./webpack/webpack.prod.js", "build:UAT": "cross-env NODE_ENV=uat webpack --config ./webpack/webpack.prod.js", "build:DEV": "cross-env NODE_ENV=development webpack --config ./webpack/webpack.prod.js", "start": "cross-env NODE_ENV=uat webpack serve --config ./webpack/webpack.prod.js", "start:PROD": "cross-env NODE_ENV=production webpack serve --config ./webpack/webpack.prod.js", "dev": "cross-env NODE_ENV=development webpack serve --config ./webpack/webpack.prod.js", "prepare": "husky install", "lint": "eslint --fix --ext .ts,.tsx src/**/*", "precommit": "biome check --write src/**/* --diagnostic-level=error --staged", "migrate-eslint": "biome migrate eslint --write", "migrate-prettier": "biome migrate prettier --write"}, "repository": {"type": "git", "url": "git+https://github.com/effihr/ui-effi-hr.git"}, "keywords": ["react", "javascript", "saas"], "author": "<EMAIL>", "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/effihr/ui-effi-hr/issues"}, "homepage": "https://github.com/effihr/ui-effi-hr#readme", "devDependencies": {"@babel/cli": "^7.22.15", "@babel/core": "^7.22.20", "@babel/preset-env": "^7.22.20", "@babel/preset-react": "^7.22.15", "@biomejs/biome": "2.0.4", "@tsconfig/recommended": "^1.0.8", "@types/firebase": "^3.2.1", "@types/js-cookie": "^3.0.6", "@types/react": "^18.3.1", "@types/react-big-calendar": "^1.8.9", "@types/react-dom": "^18.3.1", "@webpack-cli/generators": "^3.0.7", "babel-loader": "^9.1.3", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "dotenv-webpack": "^8.0.1", "html-loader": "^4.2.0", "html-webpack-plugin": "^5.5.3", "mini-css-extract-plugin": "^2.7.6", "style-loader": "^3.3.3", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "typescript": "^5.7.2", "webpack-bundle-analyzer": "^4.10.2", "webpack-dev-server": "^4.15.2"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.2.1", "@mui/lab": "^6.0.0-beta.20", "@mui/material": "^6.2.1", "@mui/x-charts": "^7.23.1", "@mui/x-date-pickers": "^7.23.2", "@reduxjs/toolkit": "^2.2.3", "@tanstack/react-form": "^1.2.3", "@tanstack/react-query": "^4.35.3", "@vis.gl/react-google-maps": "^1.5.0", "@xyflow/react": "^12.3.5", "axios": "^1.7.9", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "firebase": "^10.12.4", "husky": "^8.0.3", "js-cookie": "^3.0.5", "material-react-table": "^3.0.3", "react": "^18.3.1", "react-big-calendar": "^1.13.1", "react-date-range": "^2.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.11", "react-redux": "^9.1.2", "react-router-dom": "^6.16.0", "react-toastify": "^11.0.1", "webpack": "^5.97.1", "webpack-cli": "^5.1.4", "zod": "^3.24.2"}}