events {
	worker_connections 1024;
}
http {
	server {
		listen 8081;
		root /var/www/html/ui-effi-hr/;
		include mime.types;
		default_type application/octet-stream;

		#gzip compression
		gzip on;
		gzip_buffers 16 8k;
		gzip_comp_level 4;
		gzip_http_version 1.0;
		gzip_min_length 1280;
		gzip_types text/plain text/css application/x-javascript text/xml application/xml application/xml+rss text/javascript image/x-icon image/bmp image/svg+xml;
		gzip_vary on;

		location / {
			gzip_static on;
			root /var/www/html/ui-effi-hr/;
			try_files $uri $uri/ /index.html;
		}
		# Serve assetlinks.json for Android App Linking
		location /.well-known/assetlinks.json {
			root /var/www/html/ui-effi-hr; # Location where your assetlinks.json is stored
			default_type application/json;
		}
	}
}
